# 超慢跑助手小程序测试用例

## 1. 设置页面测试用例

### 1.1 音色设置测试
**测试目标**: 验证音色选择功能是否正常工作

**测试步骤**:
1. 进入设置页面
2. 点击"选择音色"picker
3. 选择不同的音色选项（男声、女声、机械音、自然音）
4. 观察界面显示是否更新
5. 点击保存设置
6. 退出页面后重新进入，检查设置是否保存

**预期结果**:
- ✅ picker能正常弹出选项列表
- ✅ 选择后界面立即更新显示
- ✅ 保存后设置能持久化存储
- ✅ 重新进入页面时能正确加载之前的设置

### 1.2 节拍设置测试
**测试目标**: 验证节拍类型选择功能

**测试步骤**:
1. 在设置页面点击"节拍类型"picker
2. 依次选择2/4拍、3/4拍、4/4拍、6/8拍
3. 观察界面显示变化
4. 保存设置并验证持久化

**预期结果**:
- ✅ 所有节拍类型都能正常选择
- ✅ 界面显示正确的节拍类型
- ✅ 设置能正确保存和加载

### 1.3 BPM设置测试
**测试目标**: 验证BPM设置的各种方式

**测试步骤**:
1. **预设选择测试**:
   - 点击"快速选择"picker
   - 选择不同的预设（超慢跑100、慢跑120等）
   - 验证BPM值是否正确更新

2. **滑块调节测试**:
   - 拖动BPM滑块
   - 验证数值在60-200范围内变化
   - 检查滑块显示的数值是否准确

3. **手动输入测试**:
   - 在输入框中输入有效数值（如150）
   - 输入超出范围的数值（如50、250）
   - 输入非数字字符
   - 验证输入验证和范围限制

**预期结果**:
- ✅ 预设选择能正确更新BPM值
- ✅ 滑块调节响应灵敏，数值准确
- ✅ 手动输入有正确的验证和范围限制
- ✅ 当前BPM显示区域实时更新

### 1.4 数据持久化测试
**测试目标**: 验证设置数据的存储和加载

**测试步骤**:
1. 设置所有选项为非默认值
2. 保存设置
3. 关闭小程序
4. 重新打开小程序，进入设置页面
5. 检查所有设置是否保持

**预期结果**:
- ✅ 所有设置都能正确保存到本地存储
- ✅ 重新打开应用后设置保持不变
- ✅ 全局数据也正确更新

### 1.5 重置功能测试
**测试目标**: 验证重置默认设置功能

**测试步骤**:
1. 修改所有设置为非默认值
2. 点击"重置默认"按钮
3. 在确认对话框中点击确认
4. 检查所有设置是否恢复默认值

**预期结果**:
- ✅ 弹出确认对话框
- ✅ 确认后所有设置恢复默认值
- ✅ 本地存储被正确清除

## 2. 首页功能测试用例

### 2.1 设置数据集成测试
**测试目标**: 验证首页能正确显示和使用设置数据

**测试步骤**:
1. 在设置页面修改音色、节拍、BPM
2. 保存设置
3. 返回首页
4. 检查设置显示区域是否正确显示当前设置
5. 开始跑步，验证节拍是否按设置的BPM播放

**预期结果**:
- ✅ 首页正确显示当前设置
- ✅ 节拍按正确的BPM频率播放
- ✅ 节拍指示器正确显示当前拍子

### 2.2 节拍播放测试
**测试目标**: 验证节拍播放功能

**测试步骤**:
1. 设置不同的BPM值（如100、120、160）
2. 开始跑步
3. 观察节拍指示器动画
4. 感受震动反馈
5. 测试节拍开关功能

**预期结果**:
- ✅ 节拍频率与设置的BPM一致
- ✅ 重音节拍有不同的视觉和触觉反馈
- ✅ 节拍开关能正常控制节拍播放

### 2.3 跑步数据记录测试
**测试目标**: 验证跑步数据的计算和记录

**测试步骤**:
1. 开始跑步
2. 运行一段时间（如2分钟）
3. 暂停/完成跑步
4. 检查距离、卡路里、配速等数据
5. 验证数据是否保存到历史记录

**预期结果**:
- ✅ 时间计算准确
- ✅ 距离、卡路里、配速计算合理
- ✅ 节拍计数正确
- ✅ 数据正确保存到历史记录

## 3. 历史页面测试用例

### 3.1 记录显示测试
**测试目标**: 验证历史记录的显示功能

**测试步骤**:
1. 完成几次跑步记录
2. 进入历史页面
3. 检查记录列表显示
4. 验证记录详情显示

**预期结果**:
- ✅ 记录按时间正确排序
- ✅ 显示完整的跑步数据
- ✅ 点击记录能查看详细信息

### 3.2 筛选和排序测试
**测试目标**: 验证记录的筛选和排序功能

**测试步骤**:
1. 创建不同时间的跑步记录
2. 测试时间筛选（全部、最近一周等）
3. 测试排序方式（按日期、时长、距离等）
4. 测试排序顺序切换

**预期结果**:
- ✅ 筛选功能正确过滤记录
- ✅ 排序功能正确排列记录
- ✅ 排序顺序切换正常

### 3.3 统计数据测试
**测试目标**: 验证统计数据的计算

**测试步骤**:
1. 创建多条跑步记录
2. 切换到统计页面
3. 验证各项统计数据的准确性

**预期结果**:
- ✅ 总次数、总时间、总距离计算正确
- ✅ 平均配速、最长跑步时间等统计准确
- ✅ 统计数据实时更新

### 3.4 记录管理测试
**测试目标**: 验证记录的删除和清空功能

**测试步骤**:
1. 删除单条记录
2. 清空所有记录
3. 验证操作的确认机制

**预期结果**:
- ✅ 删除操作有确认提示
- ✅ 删除后记录正确移除
- ✅ 清空功能正常工作

## 4. 综合测试用例

### 4.1 页面间数据同步测试
**测试目标**: 验证各页面间的数据同步

**测试步骤**:
1. 在设置页面修改设置
2. 在首页开始跑步
3. 完成跑步后查看历史记录
4. 验证数据在各页面间的一致性

**预期结果**:
- ✅ 设置数据在各页面正确同步
- ✅ 跑步记录正确保存和显示
- ✅ 统计数据实时更新

### 4.2 异常情况测试
**测试目标**: 验证异常情况的处理

**测试步骤**:
1. 测试存储空间不足的情况
2. 测试网络异常的情况
3. 测试应用被强制关闭的情况

**预期结果**:
- ✅ 有适当的错误提示
- ✅ 应用不会崩溃
- ✅ 数据尽可能保持完整

### 4.3 性能测试
**测试目标**: 验证应用的性能表现

**测试步骤**:
1. 创建大量历史记录（如100条）
2. 测试页面加载速度
3. 测试滑动和交互的流畅性

**预期结果**:
- ✅ 页面加载速度合理
- ✅ 交互响应及时
- ✅ 内存使用合理

## 测试结果记录

### 已通过的测试
- [x] 设置页面基本功能
- [x] 数据持久化
- [x] 首页设置集成
- [x] 节拍播放基础功能
- [x] 历史记录基础显示

### 待测试项目
- [ ] 完整的节拍播放测试
- [ ] 跑步数据准确性验证
- [ ] 大量数据的性能测试
- [ ] 异常情况处理测试

### 发现的问题
1. **问题**: 无
   **状态**: 待发现
   **优先级**: -

### 测试环境
- **测试设备**: 微信开发者工具
- **微信版本**: 最新版本
- **操作系统**: Windows/macOS
- **测试时间**: 2024年当前日期

### 测试总结
设置页面的核心功能已经实现并通过基础测试，包括音色选择、节拍设置、BPM调节、数据持久化等功能都工作正常。首页和历史页面的基础框架也已完成。下一步需要进行更深入的功能测试和性能优化。