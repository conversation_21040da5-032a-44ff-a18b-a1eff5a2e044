// app.js
App({
  onLaunch: function () {
    // 小程序启动时执行的逻辑
    console.log('App launched')
    
    // 初始化全局设置
    this.initGlobalSettings();
  },
  
  // 初始化全局设置
  initGlobalSettings: function() {
    try {
      const voiceIndex = wx.getStorageSync('selectedVoiceIndex') || 0;
      const beatIndex = wx.getStorageSync('selectedBeatIndex') || 2;
      const bpm = wx.getStorageSync('bpm') || 120;
      
      const voiceTypes = ['男声', '女声', '机械音', '自然音'];
      const beatTypes = ['2/4拍', '3/4拍', '4/4拍', '6/8拍'];
      
      this.globalData.settings = {
        voice: voiceTypes[voiceIndex],
        beat: beatTypes[beatIndex],
        bpm: bpm
      };
    } catch (e) {
      // 如果读取失败，使用默认设置
      this.globalData.settings = {
        voice: '男声',
        beat: '4/4拍',
        bpm: 120
      };
    }
  },
  
  globalData: {
    // 全局数据
    userInfo: null,
    settings: {
      voice: '男声',
      beat: '4/4拍',
      bpm: 120
    }
  }
})
