/**app.wxss**/
/* WeUI Base Styles */
page {
  background-color: #f8f8f8;
  font-size: 16px;
  font-family: -apple-system-font, "Helvetica Neue", sans-serif;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* WeUI Cells */
.weui-cells {
  margin-top: 20rpx;
  background-color: #fff;
  line-height: 1.41176471;
  font-size: 17px;
  overflow: hidden;
  position: relative;
}

.weui-cells:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1rpx solid #e5e5e5;
  color: #e5e5e5;
  transform-origin: 0 0;
  transform: scaleY(0.5);
}

.weui-cells:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1rpx solid #e5e5e5;
  color: #e5e5e5;
  transform-origin: 0 100%;
  transform: scaleY(0.5);
}

.weui-cell {
  padding: 10px 15px;
  position: relative;
  display: flex;
  align-items: center;
}

.weui-cell:before {
  content: " ";
  position: absolute;
  left: 15px;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1rpx solid #e5e5e5;
  color: #e5e5e5;
  transform-origin: 0 0;
  transform: scaleY(0.5);
}

.weui-cell:first-child:before {
  display: none;
}

.weui-cell__bd {
  flex: 1;
}

.weui-cell__ft {
  text-align: right;
  color: #999;
}

/* WeUI Buttons */
.weui-btn {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  padding-left: 14px;
  padding-right: 14px;
  box-sizing: border-box;
  font-size: 18px;
  text-align: center;
  text-decoration: none;
  color: #fff;
  line-height: 2.55555556;
  border-radius: 5px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  overflow: hidden;
  border: none;
}

.weui-btn_primary {
  background-color: #1aad19;
}

.weui-btn_warn {
  background-color: #e64340;
}

.weui-btn_default {
  color: #000;
  background-color: #f8f8f8;
}

.weui-btn_mini {
  display: inline-block;
  line-height: 2.3;
  font-size: 13px;
  padding: 0 1.32em;
}

/* WeUI Icons */
.weui-icon {
  width: 24px;
  height: 24px;
  display: inline-block;
  vertical-align: middle;
}

/* WeUI Grid */
.weui-grids {
  border-top: 1rpx solid #e5e5e5;
  border-left: 1rpx solid #e5e5e5;
  background-color: #fff;
}

.weui-grid {
  position: relative;
  float: left;
  padding: 20px 10px;
  width: 33.33333333%;
  box-sizing: border-box;
  border-right: 1rpx solid #e5e5e5;
  border-bottom: 1rpx solid #e5e5e5;
}

/* WeUI Panel */
.weui-panel {
  background-color: #fff;
  margin-top: 10px;
  position: relative;
  overflow: hidden;
}

.weui-panel:first-child {
  margin-top: 0;
}

.weui-panel:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1rpx solid #e5e5e5;
  color: #e5e5e5;
  transform-origin: 0 0;
  transform: scaleY(0.5);
}

.weui-panel:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1rpx solid #e5e5e5;
  color: #e5e5e5;
  transform-origin: 0 100%;
  transform: scaleY(0.5);
}

.weui-panel__hd {
  padding: 14px 15px 10px;
  color: #999;
  font-size: 13px;
  position: relative;
}

.weui-panel__bd {
  overflow: hidden;
}

.weui-media-box {
  padding: 15px;
  position: relative;
}

.weui-media-box:before {
  content: " ";
  position: absolute;
  left: 15px;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1rpx solid #e5e5e5;
  color: #e5e5e5;
  transform-origin: 0 0;
  transform: scaleY(0.5);
}

.weui-media-box:first-child:before {
  display: none;
}

/* WeUI Badge */
.weui-badge {
  display: inline-block;
  padding: .15em .4em;
  min-width: 8px;
  border-radius: 18px;
  background-color: #e64340;
  color: #fff;
  line-height: 1.2;
  text-align: center;
  font-size: 12px;
  vertical-align: middle;
}

/* WeUI Loading */
.weui-loading {
  margin: 0 5px;
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: middle;
  animation: weuiLoading 1s steps(12, end) infinite;
  background: transparent url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJhIj48c3RvcCBvZmZzZXQ9IjAlIiBzdG9wLWNvbG9yPSIjNzBiN2ZkIiBzdG9wLW9wYWNpdHk9IjAiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM0Mjg1ZjQiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDYwIDYwKSI+PGNpcmNsZSBmaWxsPSJ1cmwoI2EpIiByPSI1MCIvPjwvZz48L2c+PC9zdmc+) no-repeat;
  background-size: 100%;
}

@keyframes weuiLoading {
  0% { transform: rotate3d(0, 0, 1, 0deg); }
  100% { transform: rotate3d(0, 0, 1, 360deg); }
}