/**index.wxss**/
/* 本地图标样式 */

.page {
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* WeUI导航栏样式 */
.weui-navigation-bar {
  position: relative;
  background-color: #1aad19;
  color: #fff;
  text-align: center;
}

.weui-navigation-bar__inner {
  position: relative;
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 15px;
}

.weui-navigation-bar__left,
.weui-navigation-bar__right {
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  height: 44px;
}

.weui-navigation-bar__left {
  left: 15px;
}

.weui-navigation-bar__right {
  right: 15px;
}

.weui-navigation-bar__center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
}

.weui-navigation-bar__title {
  font-size: 17px;
  font-weight: 600;
}

.weui-navigation-bar__btn {
  padding: 8px;
  color: #fff;
  font-size: 18px;
}

/* 计时器面板样式 */
.timer-panel {
  margin: 20rpx;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  text-align: center;
  padding: 40rpx 20rpx;
}

.timer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.timer-display {
  font-size: 120rpx;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  margin-bottom: 20rpx;
  text-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
}

/* 节拍指示器 */
.beat-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20rpx;
}

.beat-circle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
  animation: pulse 0.6s ease-in-out infinite;
  font-size: 24rpx;
}

.beat-circle.accent {
  background: #ff6b6b;
  animation: accentPulse 0.6s ease-in-out infinite;
}

.beat-text {
  font-size: 24rpx;
  opacity: 0.8;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.6; }
  50% { transform: scale(1.2); opacity: 1; }
}

@keyframes accentPulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.4); opacity: 1; }
}

/* WeUI网格样式重写 */
.weui-grids {
  display: flex;
  flex-wrap: wrap;
  border: none;
  background-color: transparent;
}

.weui-grid {
  width: 50%;
  padding: 20rpx;
  text-align: center;
  border: none;
  background-color: #fff;
  margin: 10rpx;
  border-radius: 12px;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.weui-grid:nth-child(odd) {
  margin-right: 5rpx;
}

.weui-grid:nth-child(even) {
  margin-left: 5rpx;
}

.weui-grid__icon {
  margin-bottom: 10rpx;
  font-size: 32rpx;
  color: #1aad19;
}

.weui-grid__label {
  font-size: 14px;
  color: #333;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* WeUI按钮区域 */
.weui-btn-area {
  margin: 30rpx 30rpx 0;
}

.weui-btn {
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  border-radius: 8px;
}

.weui-btn .btn-icon {
  margin-right: 8rpx;
  font-size: 18px;
}

.weui-btn:disabled {
  opacity: 0.6;
}

.finish-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border: none;
}

/* WeUI开关样式 */
.weui-cell_switch .weui-cell__ft {
  padding-left: 15px;
}

.weui-cell__hd .cell-icon {
  margin-right: 15rpx;
  font-size: 20px;
}

/* 图标样式 */
.nav-icon, .cell-icon, .grid-icon, .btn-icon, .beat-icon {
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.nav-icon {
  font-size: 18px;
}

.cell-icon {
  font-size: 20px;
}

.grid-icon {
  font-size: 32rpx;
}

.btn-icon {
  font-size: 18px;
}

.beat-icon {
  font-size: 24rpx;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .timer-display {
    font-size: 100rpx;
  }
  
  .weui-grid {
    width: 100%;
    margin: 5rpx 0;
  }
  
  .weui-grid:nth-child(odd),
  .weui-grid:nth-child(even) {
    margin-left: 0;
    margin-right: 0;
  }
  
  .weui-btn-area {
    margin: 20rpx 20rpx 0;
  }
}

@media (min-width: 768px) {
  .page {
    max-width: 800rpx;
    margin: 0 auto;
  }
  
  .weui-grid {
    width: 23%;
    margin: 10rpx 1%;
  }
}