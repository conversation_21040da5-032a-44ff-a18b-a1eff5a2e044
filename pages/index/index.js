// index.js
const app = getApp()
const { audioManager } = require('../../utils/simple-audio.js')
const { metronome } = require('../../utils/metronome.js')
Page({
  data: {
    time: '00:00:00',
    isRunning: false,
    startTime: 0,
    pausedTime: 0, // 累计暂停前的时间（毫秒）
    interval: null,
    
    // 节拍相关数据
    beatInterval: null,
    beatCount: 0,
    totalBeats: 0,
    isBeatEnabled: true,
    
    // 设置数据
    settings: {
      voice: '男声',
      beat: '4/4拍',
      bpm: 120
    },
    
    // 跑步数据
    distance: 0,
    calories: 0,
    avgPace: '00:00',
    
    // 界面状态
    showStats: false,
    currentBeatInMeasure: 1,
    
    // 音频上下文
    audioContext: null,
    isAudioReady: false
  },
  
  onLoad: function () {
    // 页面加载时执行的逻辑
    this.loadSettings();
    this.initAudio();
  },
  
  onShow: function () {
    // 页面显示时重新加载设置（从设置页面返回时）
    this.loadSettings();
  },
  
  onUnload: function () {
    // 页面卸载时清理资源
    this.cleanup();
  },
  
  onHide: function () {
    // 页面隐藏时暂停节拍（但不停止计时）
    if (this.data.beatInterval) {
      clearInterval(this.data.beatInterval);
      this.setData({ beatInterval: null });
    }
  },
  
  // 加载设置数据
  loadSettings: function() {
    const settings = app.globalData.settings || {
      voice: '男声',
      beat: '4/4拍',
      bpm: 120
    };
    
    this.setData({
      settings: settings
    });
    
    // 更新音频和节拍器设置
    if (this.data.isAudioReady) {
      audioManager.setVoice(settings.voice);
      metronome.setBPM(settings.bpm);
      metronome.setBeatType(settings.beat);
      metronome.setVoice(settings.voice);
    }
  },
  
  // 初始化音频
  initAudio: function() {
    try {
      // 设置音频管理器
      audioManager.setVoice(this.data.settings.voice);
      audioManager.setEnabled(true);
      
      // 设置节拍器
      metronome.setBPM(this.data.settings.bpm);
      metronome.setBeatType(this.data.settings.beat);
      metronome.setVoice(this.data.settings.voice);
      
      // 设置节拍器回调
      metronome.setCallbacks({
        onBeat: (beat, isAccent, totalBeats) => {
          this.setData({
            currentBeatInMeasure: beat,
            totalBeats: totalBeats
          });
        },
        onMeasure: (measureCount) => {
          console.log(`完成第 ${measureCount} 小节`);
        },
        onAccent: (beat, measureCount) => {
          console.log(`重音拍: 第 ${measureCount} 小节第 ${beat} 拍`);
        }
      });
      
      this.setData({ isAudioReady: true });
    } catch (e) {
      console.log('音频初始化失败:', e);
      this.setData({ isAudioReady: false });
    }
  },
  
  // 开始跑步
  startTimer: function () {
    if (!this.data.isRunning) {
      // 记录当前开始时间
      this.setData({
        startTime: Date.now(),
        isRunning: true,
        showStats: true
      });
      
      // 开始计时
      this.data.interval = setInterval(() => {
        // 计算总的运行时间 = 之前累计的时间 + 本次运行时间
        const currentSessionTime = Date.now() - this.data.startTime;
        const totalElapsed = this.data.pausedTime + currentSessionTime;
        
        this.setData({
          time: this.formatTime(totalElapsed)
        });
        
        // 更新跑步数据
        this.updateRunningStats(totalElapsed);
      }, 1000);
      
      // 开始节拍
      if (this.data.isBeatEnabled) {
        // 如果是继续跑步，设置节拍器的初始计数
        if (this.data.pausedTime > 0) {
          metronome.setTotalBeats(this.data.totalBeats);
        }
        metronome.start();
      }
      
      // 保存开始时间到历史记录（只在第一次开始时保存）
      if (this.data.pausedTime === 0) {
        this.saveRunStart();
      }
      
      console.log('开始跑步，累计时间:', this.data.pausedTime, 'ms');
    }
  },
  
  // 暂停跑步
  pauseTimer: function () {
    if (this.data.isRunning) {
      clearInterval(this.data.interval);
      metronome.stop(); // 只停止节拍器，不重置计数
      
      // 计算本次运行的时间并累加到总时间
      const currentSessionTime = Date.now() - this.data.startTime;
      const newPausedTime = this.data.pausedTime + currentSessionTime;
      
      // 保存当前的总节拍数
      const currentTotalBeats = metronome.getStatus().totalBeats;
      
      this.setData({
        isRunning: false,
        pausedTime: newPausedTime,
        time: this.formatTime(newPausedTime),
        totalBeats: currentTotalBeats // 保存当前节拍数
      });
      
      console.log('暂停跑步，本次运行时间:', currentSessionTime, 'ms，累计时间:', newPausedTime, 'ms，总节拍数:', currentTotalBeats);
    }
  },
  
  // 重置跑步
  resetTimer: function () {
    this.cleanup();
    
    // 重置节拍器计数
    metronome.reset();
    
    this.setData({
      time: '00:00:00',
      isRunning: false,
      startTime: 0,
      pausedTime: 0, // 重置累计时间
      beatCount: 0,
      totalBeats: 0,
      distance: 0,
      calories: 0,
      avgPace: '00:00',
      showStats: false,
      currentBeatInMeasure: 1
    });
    
    console.log('重置跑步数据');
  },
  
  // 完成跑步
  finishRun: function() {
    if (this.data.isRunning) {
      this.pauseTimer();
    }
    
    // 播放完成音效
    audioManager.playNotification('finish');
    
    // 保存跑步记录
    this.saveRunRecord();
    
    // 获取节拍器统计
    const metronomeStats = metronome.getAccuracyStats();
    const statsText = metronomeStats ? 
      `\n节拍精度: ±${metronomeStats.average}ms` : '';
    
    wx.showModal({
      title: '🏁 跑步完成',
      content: `本次跑步时间：${this.data.time}\n距离：${this.data.distance.toFixed(2)}km\n消耗卡路里：${this.data.calories}\n总节拍数：${this.data.totalBeats}${statsText}`,
      confirmText: '查看详情',
      cancelText: '继续跑步',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/history/history'
          });
        }
      }
    });
  },
  
  // 测试音效功能
  testAudio: function() {
    audioManager.testAudio();
  },
  
  // 校准节拍器
  calibrateMetronome: function() {
    metronome.calibrate();
  },
  
  // 切换节拍开关
  toggleBeat: function() {
    const newState = !this.data.isBeatEnabled;
    this.setData({ isBeatEnabled: newState });
    
    if (this.data.isRunning) {
      if (newState) {
        metronome.start();
      } else {
        metronome.stop();
      }
    }
    
    // 播放切换提示音
    audioManager.playNotification(newState ? 'success' : 'warning');
    
    wx.showToast({
      title: newState ? '节拍已开启' : '节拍已关闭',
      icon: 'success',
      duration: 1000
    });
  },
  
  // 更新跑步统计数据
  updateRunningStats: function(elapsed) {
    const minutes = elapsed / 60000;
    
    // 简单的距离估算（基于时间和平均速度）
    const avgSpeed = 8; // km/h，可以根据用户设置调整
    const distance = (minutes / 60) * avgSpeed;
    
    // 卡路里估算（基于时间和体重）
    const weight = 65; // kg，可以从用户设置获取
    const calories = Math.floor((minutes * weight * 0.1));
    
    // 平均配速计算
    const avgPace = distance > 0 ? this.formatPace(minutes / distance) : '00:00';
    
    this.setData({
      distance: distance,
      calories: calories,
      avgPace: avgPace
    });
  },
  
  // 格式化配速
  formatPace: function(paceMinutes) {
    const minutes = Math.floor(paceMinutes);
    const seconds = Math.floor((paceMinutes - minutes) * 60);
    return `${this.pad(minutes)}:${this.pad(seconds)}`;
  },
  
  // 保存跑步开始记录
  saveRunStart: function() {
    const startRecord = {
      startTime: new Date().toISOString(),
      settings: this.data.settings
    };
    
    try {
      wx.setStorageSync('currentRun', startRecord);
    } catch (e) {
      console.log('保存开始记录失败:', e);
    }
  },
  
  // 保存跑步记录
  saveRunRecord: function() {
    const record = {
      id: Date.now(),
      date: new Date().toISOString(),
      duration: this.data.time,
      distance: this.data.distance,
      calories: this.data.calories,
      avgPace: this.data.avgPace,
      totalBeats: this.data.totalBeats,
      settings: this.data.settings
    };
    
    try {
      let records = wx.getStorageSync('runRecords') || [];
      records.unshift(record);
      
      // 只保留最近100条记录
      if (records.length > 100) {
        records = records.slice(0, 100);
      }
      
      wx.setStorageSync('runRecords', records);
      wx.removeStorageSync('currentRun');
    } catch (e) {
      console.log('保存跑步记录失败:', e);
    }
  },
  
  // 跳转到设置页面
  goToSettings: function() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },
  
  // 清理资源
  cleanup: function() {
    if (this.data.interval) {
      clearInterval(this.data.interval);
      this.setData({ interval: null });
    }
    
    // 停止节拍器（但不重置计数，除非是真正的重置操作）
    metronome.stop();
    
    // 清理音频资源
    if (this.audioContext) {
      this.audioContext.destroy();
    }
  },
  
  formatTime: function (milliseconds) {
    const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    return `${this.pad(hours)}:${this.pad(minutes % 60)}:${this.pad(seconds % 60)}`
  },
  
  pad: function (num) {
    return num < 10 ? `0${num}` : num
  },
  
  timeToMilliseconds: function (timeString) {
    const [hours, minutes, seconds] = timeString.split(':').map(Number)
    return (hours * 3600 + minutes * 60 + seconds) * 1000
  }
})
