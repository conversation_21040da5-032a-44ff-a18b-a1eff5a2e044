<!--index.wxml-->
<view class="page">
  <!-- WeUI导航栏 -->
  <view class="weui-navigation-bar">
    <view class="weui-navigation-bar__inner">
      <view class="weui-navigation-bar__left">
        <view class="weui-navigation-bar__btn" bindtap="goToSettings">
          <text class="nav-icon">⚙️</text>
        </view>
      </view>
      <view class="weui-navigation-bar__center">
        <view class="weui-navigation-bar__title">超慢跑助手</view>
      </view>
      <view class="weui-navigation-bar__right">
        <view class="weui-navigation-bar__btn" bindtap="testAudio">
          <text class="nav-icon">🎵</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 当前设置面板 -->
  <view class="weui-panel">
    <view class="weui-panel__hd">当前设置</view>
    <view class="weui-panel__bd">
      <view class="weui-cells">
        <view class="weui-cell">
          <view class="weui-cell__hd">
            <text class="cell-icon">🎤</text>
          </view>
          <view class="weui-cell__bd">音色</view>
          <view class="weui-cell__ft">{{settings.voice}}</view>
        </view>
        <view class="weui-cell">
          <view class="weui-cell__hd">
            <text class="cell-icon">🎵</text>
          </view>
          <view class="weui-cell__bd">节拍</view>
          <view class="weui-cell__ft">{{settings.beat}}</view>
        </view>
        <view class="weui-cell">
          <view class="weui-cell__hd">
            <text class="cell-icon">⚡</text>
          </view>
          <view class="weui-cell__bd">BPM</view>
          <view class="weui-cell__ft">{{settings.bpm}}</view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 主计时器面板 -->
  <view class="weui-panel timer-panel">
    <view class="timer-container">
      <view class="timer-display">{{time}}</view>
      <view class="beat-indicator" wx:if="{{isBeatEnabled && isRunning}}">
        <view class="beat-circle {{currentBeatInMeasure === 1 ? 'accent' : ''}}">
          <text class="beat-icon">💓</text>
        </view>
        <text class="beat-text">{{currentBeatInMeasure}}/{{settings.beat.charAt(0)}}</text>
      </view>
    </view>
  </view>
  
  <!-- 跑步统计面板 -->
  <view class="weui-panel" wx:if="{{showStats}}">
    <view class="weui-panel__hd">跑步数据</view>
    <view class="weui-panel__bd">
      <view class="weui-grids">
        <view class="weui-grid">
          <view class="weui-grid__icon">
            <text class="grid-icon">📏</text>
          </view>
          <view class="weui-grid__label">
            <view class="stat-value">{{distance.toFixed(2)}}</view>
            <view class="stat-label">公里</view>
          </view>
        </view>
        <view class="weui-grid">
          <view class="weui-grid__icon">
            <text class="grid-icon">🔥</text>
          </view>
          <view class="weui-grid__label">
            <view class="stat-value">{{calories}}</view>
            <view class="stat-label">卡路里</view>
          </view>
        </view>
        <view class="weui-grid">
          <view class="weui-grid__icon">
            <text class="grid-icon">⚡</text>
          </view>
          <view class="weui-grid__label">
            <view class="stat-value">{{avgPace}}</view>
            <view class="stat-label">配速/km</view>
          </view>
        </view>
        <view class="weui-grid">
          <view class="weui-grid__icon">
            <text class="grid-icon">💓</text>
          </view>
          <view class="weui-grid__label">
            <view class="stat-value">{{totalBeats}}</view>
            <view class="stat-label">总节拍</view>
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 节拍控制面板 -->
  <view class="weui-panel">
    <view class="weui-panel__bd">
      <view class="weui-cells">
        <view class="weui-cell weui-cell_switch">
          <view class="weui-cell__hd">
            <text class="cell-icon">🔊</text>
          </view>
          <view class="weui-cell__bd">节拍提示</view>
          <view class="weui-cell__ft">
            <switch checked="{{isBeatEnabled}}" bindchange="toggleBeat" />
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 主控制按钮 -->
  <view class="weui-btn-area">
    <button class="weui-btn weui-btn_primary" bindtap="startTimer" disabled="{{isRunning}}">
      <text class="btn-icon">▶️</text>
      开始跑步
    </button>
    <button class="weui-btn weui-btn_warn" bindtap="pauseTimer" disabled="{{!isRunning}}">
      <text class="btn-icon">⏸️</text>
      暂停
    </button>
    <button class="weui-btn weui-btn_default" bindtap="resetTimer">
      <text class="btn-icon">🔄</text>
      重置
    </button>
  </view>
  
  <!-- 完成按钮 -->
  <view class="weui-btn-area" wx:if="{{isRunning || time !== '00:00:00'}}">
    <button class="weui-btn weui-btn_primary finish-btn" bindtap="finishRun">
      <text class="btn-icon">🏁</text>
      完成跑步
    </button>
  </view>
</view>
