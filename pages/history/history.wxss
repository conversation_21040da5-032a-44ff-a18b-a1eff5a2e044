/**history.wxss**/
.container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx 0;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.export-btn, .clear-btn {
  width: 60rpx;
  height: 60rpx;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.clear-btn {
  background: #ff3b30;
  color: #fff;
}

/* Tab bar */
.tab-bar {
  display: flex;
  width:80%;
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-content{
  display: table-column;
  width: 80%;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 32rpx;
  color: #666;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  font-weight: bold;
}

/* Filter controls */
.filter-controls {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  gap: 30rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  flex: 1;
}

.filter-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 15rpx;
}

.filter-picker {
  flex: 1;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
}

.sort-order-btn {
  width: 60rpx;
  height: 60rpx;
  background: #007aff;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
}

/* Records list */
.records-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.record-item {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.record-item:active {
  transform: scale(0.98);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.record-date {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.record-actions {
  display: flex;
  gap: 15rpx;
}

.delete-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.record-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
  gap: 20rpx;
}

.stat-group {
  display: flex;
  gap: 30rpx;
  flex: 1;
  min-width: 280rpx;
}

.stat-item {
  text-align: center;
  flex: 1;
  min-width: 120rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.record-settings {
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

.setting-text {
  font-size: 24rpx;
  color: #999;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 36rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.empty-tip {
  font-size: 28rpx;
  color: #999;
}

/* Statistics grid */
.statistics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stat-card-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.stat-card-label {
  font-size: 24rpx;
  color: #666;
}

/* Detail modal */
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.detail-content {
  width: 90%;
  max-width: 600rpx;
  max-height: 80%;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
}

.detail-title {
  font-size: 36rpx;
  font-weight: bold;
}

.detail-close {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.detail-info {
  padding: 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  text-align: right;
  flex: 1;
}

.detail-section {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 4rpx solid #f0f0f0;
}

.detail-section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* Responsive design */
@media (max-width: 375px) {
  .container {
    padding: 15rpx;
  }
  
  .statistics-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-controls {
    flex-direction: column;
    gap: 20rpx;
    padding: 20rpx;
  }
  
  .filter-item {
    width: 100%;
  }
  
  .record-item {
    padding: 20rpx;
  }
  
  .record-stats {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .stat-group {
    justify-content: space-between;
    gap: 20rpx;
    min-width: auto;
  }
  
  .stat-item {
    min-width: 100rpx;
  }
  
  .stat-value {
    font-size: 28rpx;
  }
  
  .stat-label {
    font-size: 22rpx;
  }
}

/* Large screen optimization */
@media (min-width: 768px) {
  .container {
    max-width: 800rpx;
    margin: 0 auto;
  }
  
  .record-stats {
    justify-content: space-around;
  }
  
  .stat-group {
    gap: 50rpx;
  }
  
  .stat-item {
    min-width: 150rpx;
  }
}