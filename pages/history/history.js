// history.js
const util = require('../../utils/util.js')

Page({
  data: {
    records: [],
    statistics: {
      totalRuns: 0,
      totalTime: '00:00:00',
      totalDistance: 0,
      totalCalories: 0,
      avgPace: '00:00',
      longestRun: '00:00:00',
      bestPace: '00:00'
    },
    currentTab: 0,
    tabList: ['记录', '统计'],
    
    // 筛选和排序
    sortBy: 'date', // date, duration, distance, calories
    sortOrder: 'desc', // asc, desc
    filterPeriod: 'all', // all, week, month, year
    
    // 分页
    pageSize: 10,
    currentPage: 1,
    hasMore: true,
    
    // 详情弹窗
    showDetail: false,
    selectedRecord: null
  },
  
  onLoad: function () {
    this.loadRecords();
    this.calculateStatistics();
  },
  
  onShow: function () {
    // 页面显示时重新加载数据（从首页返回时可能有新记录）
    this.loadRecords();
    this.calculateStatistics();
  },
  
  // 加载跑步记录
  loadRecords: function() {
    try {
      let records = wx.getStorageSync('runRecords') || [];
      
      // 应用筛选
      records = this.filterRecords(records);
      
      // 应用排序
      records = this.sortRecords(records);
      
      // 预处理日期格式
      records = records.map(record => ({
        ...record,
        formattedDate: this.formatDate(record.date)
      }));
      
      this.setData({
        records: records,
        hasMore: records.length > this.data.pageSize * this.data.currentPage
      });
    } catch (e) {
      console.log('加载记录失败:', e);
      this.setData({ records: [] });
    }
  },
  
  // 筛选记录
  filterRecords: function(records) {
    if (this.data.filterPeriod === 'all') {
      return records;
    }
    
    const now = new Date();
    const filterDate = new Date();
    
    switch (this.data.filterPeriod) {
      case 'week':
        filterDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        filterDate.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        filterDate.setFullYear(now.getFullYear() - 1);
        break;
    }
    
    return records.filter(record => {
      const recordDate = new Date(record.date);
      return recordDate >= filterDate;
    });
  },
  
  // 排序记录
  sortRecords: function(records) {
    return records.sort((a, b) => {
      let aValue, bValue;
      
      switch (this.data.sortBy) {
        case 'date':
          aValue = new Date(a.date);
          bValue = new Date(b.date);
          break;
        case 'duration':
          aValue = this.timeToSeconds(a.duration);
          bValue = this.timeToSeconds(b.duration);
          break;
        case 'distance':
          aValue = a.distance || 0;
          bValue = b.distance || 0;
          break;
        case 'calories':
          aValue = a.calories || 0;
          bValue = b.calories || 0;
          break;
        default:
          return 0;
      }
      
      if (this.data.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  },
  
  // 计算统计数据
  calculateStatistics: function() {
    try {
      const allRecords = wx.getStorageSync('runRecords') || [];
      
      if (allRecords.length === 0) {
        return;
      }
      
      let totalSeconds = 0;
      let totalDistance = 0;
      let totalCalories = 0;
      let longestRunSeconds = 0;
      let bestPaceSeconds = Infinity;
      
      allRecords.forEach(record => {
        const seconds = this.timeToSeconds(record.duration);
        totalSeconds += seconds;
        totalDistance += record.distance || 0;
        totalCalories += record.calories || 0;
        
        if (seconds > longestRunSeconds) {
          longestRunSeconds = seconds;
        }
        
        if (record.avgPace && record.avgPace !== '00:00') {
          const paceSeconds = this.timeToSeconds(record.avgPace);
          if (paceSeconds < bestPaceSeconds) {
            bestPaceSeconds = paceSeconds;
          }
        }
      });
      
      const avgPaceSeconds = totalDistance > 0 ? (totalSeconds / 60) / totalDistance : 0;
      
      this.setData({
        statistics: {
          totalRuns: allRecords.length,
          totalTime: this.secondsToTime(totalSeconds),
          totalDistance: totalDistance.toFixed(2),
          totalCalories: totalCalories,
          avgPace: avgPaceSeconds > 0 ? this.formatPace(avgPaceSeconds) : '00:00',
          longestRun: this.secondsToTime(longestRunSeconds),
          bestPace: bestPaceSeconds !== Infinity ? this.secondsToTime(bestPaceSeconds) : '00:00'
        }
      });
    } catch (e) {
      console.log('计算统计失败:', e);
    }
  },
  
  // 切换标签页
  switchTab: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({ currentTab: index });
  },
  
  // 改变排序方式
  changeSortBy: function(e) {
    const sortBy = e.detail.value;
    const sortOptions = ['date', 'duration', 'distance', 'calories'];
    
    this.setData({ 
      sortBy: sortOptions[sortBy],
      currentPage: 1
    });
    this.loadRecords();
  },
  
  // 切换排序顺序
  toggleSortOrder: function() {
    this.setData({ 
      sortOrder: this.data.sortOrder === 'asc' ? 'desc' : 'asc',
      currentPage: 1
    });
    this.loadRecords();
  },
  
  // 改变筛选周期
  changeFilterPeriod: function(e) {
    const periods = ['all', 'week', 'month', 'year'];
    const period = periods[e.detail.value];
    
    this.setData({ 
      filterPeriod: period,
      currentPage: 1
    });
    this.loadRecords();
    this.calculateStatistics();
  },
  
  // 显示记录详情
  showRecordDetail: function(e) {
    const index = e.currentTarget.dataset.index;
    const record = this.data.records[index];
    
    this.setData({
      selectedRecord: record,
      showDetail: true
    });
  },
  
  // 隐藏详情弹窗
  hideDetail: function() {
    this.setData({
      showDetail: false,
      selectedRecord: null
    });
  },
  
  // 删除记录
  deleteRecord: function(e) {
    const index = e.currentTarget.dataset.index;
    const record = this.data.records[index];
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条跑步记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.performDelete(record.id);
        }
      }
    });
  },
  
  // 执行删除操作
  performDelete: function(recordId) {
    try {
      let records = wx.getStorageSync('runRecords') || [];
      records = records.filter(record => record.id !== recordId);
      wx.setStorageSync('runRecords', records);
      
      this.loadRecords();
      this.calculateStatistics();
      
      wx.showToast({
        title: '删除成功',
        icon: 'success',
        duration: 1500
      });
    } catch (e) {
      wx.showToast({
        title: '删除失败',
        icon: 'error',
        duration: 1500
      });
    }
  },
  
  // 清空所有记录
  clearAllRecords: function() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有跑步记录吗？此操作不可恢复！',
      confirmColor: '#ff3b30',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('runRecords');
            this.setData({ records: [] });
            this.calculateStatistics();
            
            wx.showToast({
              title: '已清空',
              icon: 'success',
              duration: 1500
            });
          } catch (e) {
            wx.showToast({
              title: '清空失败',
              icon: 'error',
              duration: 1500
            });
          }
        }
      }
    });
  },
  
  // 导出数据
  exportData: function() {
    try {
      const records = wx.getStorageSync('runRecords') || [];
      const dataStr = JSON.stringify(records, null, 2);
      
      // 由于小程序限制，这里只能复制到剪贴板
      wx.setClipboardData({
        data: dataStr,
        success: () => {
          wx.showToast({
            title: '数据已复制到剪贴板',
            icon: 'success',
            duration: 2000
          });
        }
      });
    } catch (e) {
      wx.showToast({
        title: '导出失败',
        icon: 'error',
        duration: 1500
      });
    }
  },
  
  // 格式化日期
  formatDate: function(dateStr) {
    const date = new Date(dateStr);
    const now = new Date();
    const diffTime = now - date;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return '今天';
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return util.formatTime(date).split(' ')[0];
    }
  },
  
  // 格式化配速
  formatPace: function(paceMinutes) {
    const minutes = Math.floor(paceMinutes);
    const seconds = Math.floor((paceMinutes - minutes) * 60);
    return `${this.pad(minutes)}:${this.pad(seconds)}`;
  },
  
  // 时间转秒数
  timeToSeconds: function(timeStr) {
    const parts = timeStr.split(':');
    return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2]);
  },
  
  // 秒数转时间
  secondsToTime: function(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${this.pad(hours)}:${this.pad(minutes)}:${this.pad(secs)}`;
  },
  
  // 补零
  pad: function(num) {
    return num < 10 ? `0${num}` : num;
  }
})
