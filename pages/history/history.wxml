<!--history.wxml-->
<view class="container">
  <!-- 标题栏 -->
  <view class="header">
    <view class="title">跑步历史</view>
    <view class="header-actions">
      <view class="export-btn" bindtap="exportData">📤</view>
      <view class="clear-btn" bindtap="clearAllRecords">🗑️</view>
    </view>
  </view>
  
  <!-- 标签页切换 -->
  <view class="tab-bar">
    <view 
      class="tab-item {{currentTab === index ? 'active' : ''}}"
      wx:for="{{tabList}}"
      wx:key="index"
      data-index="{{index}}"
      bindtap="switchTab"
    >
      {{item}}
    </view>
  </view>
  
  <!-- 记录页面 -->
  <view class="tab-content" wx:if="{{currentTab === 0}}">
    <!-- 筛选和排序控制 -->
    <view class="filter-controls">
      <view class="filter-item">
        <text class="filter-label">时间:</text>
        <picker 
          bindchange="changeFilterPeriod" 
          range="['全部', '最近一周', '最近一月', '最近一年']"
          value="{{filterPeriod === 'all' ? 0 : filterPeriod === 'week' ? 1 : filterPeriod === 'month' ? 2 : 3}}"
        >
          <view class="filter-picker">
            {{filterPeriod === 'all' ? '全部' : filterPeriod === 'week' ? '最近一周' : filterPeriod === 'month' ? '最近一月' : '最近一年'}}
          </view>
        </picker>
      </view>
      
      <view class="filter-item">
        <text class="filter-label">排序:</text>
        <picker 
          bindchange="changeSortBy" 
          range="['按日期', '按时长', '按距离', '按卡路里']"
          value="{{sortBy === 'date' ? 0 : sortBy === 'duration' ? 1 : sortBy === 'distance' ? 2 : 3}}"
        >
          <view class="filter-picker">
            {{sortBy === 'date' ? '按日期' : sortBy === 'duration' ? '按时长' : sortBy === 'distance' ? '按距离' : '按卡路里'}}
          </view>
        </picker>
      </view>
      
      <view class="sort-order-btn" bindtap="toggleSortOrder">
        {{sortOrder === 'desc' ? '↓' : '↑'}}
      </view>
    </view>
    
    <!-- 记录列表 -->
    <view class="records-list" wx:if="{{records.length > 0}}">
      <view 
        class="record-item" 
        wx:for="{{records}}" 
        wx:key="id"
        data-index="{{index}}"
        bindtap="showRecordDetail"
      >
        <view class="record-header">
          <view class="record-date">{{item.formattedDate}}</view>
          <view class="record-actions">
            <view class="delete-btn" data-index="{{index}}" catch:tap="deleteRecord">
              ❌
            </view>
          </view>
        </view>
        
        <view class="record-stats">
          <view class="stat-group">
            <view class="stat-item">
              <text class="stat-value">{{item.duration}}</text>
              <text class="stat-label">时长</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{item.distance ? item.distance.toFixed(2) : '0.00'}}</text>
              <text class="stat-label">公里</text>
            </view>
          </view>
          
          <view class="stat-group">
            <view class="stat-item">
              <text class="stat-value">{{item.calories || 0}}</text>
              <text class="stat-label">卡路里</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{item.avgPace || '00:00'}}</text>
              <text class="stat-label">配速</text>
            </view>
          </view>
        </view>
        
        <view class="record-settings">
          <text class="setting-text">{{item.settings.voice}} | {{item.settings.beat}} | {{item.settings.bpm}}BPM</text>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:else>
      <view class="empty-icon">🏃‍♂️</view>
      <view class="empty-text">还没有跑步记录</view>
      <view class="empty-tip">开始你的第一次跑步吧！</view>
    </view>
  </view>
  
  <!-- 统计页面 -->
  <view class="tab-content" wx:if="{{currentTab === 1}}">
    <view class="statistics-grid">
      <view class="stat-card">
        <view class="stat-card-value">{{statistics.totalRuns}}</view>
        <view class="stat-card-label">总跑步次数</view>
      </view>
      
      <view class="stat-card">
        <view class="stat-card-value">{{statistics.totalTime}}</view>
        <view class="stat-card-label">总跑步时间</view>
      </view>
      
      <view class="stat-card">
        <view class="stat-card-value">{{statistics.totalDistance}}km</view>
        <view class="stat-card-label">总跑步距离</view>
      </view>
      
      <view class="stat-card">
        <view class="stat-card-value">{{statistics.totalCalories}}</view>
        <view class="stat-card-label">总消耗卡路里</view>
      </view>
      
      <view class="stat-card">
        <view class="stat-card-value">{{statistics.avgPace}}</view>
        <view class="stat-card-label">平均配速</view>
      </view>
      
      <view class="stat-card">
        <view class="stat-card-value">{{statistics.longestRun}}</view>
        <view class="stat-card-label">最长跑步时间</view>
      </view>
    </view>
  </view>
  
  <!-- 详情弹窗 -->
  <view class="detail-modal" wx:if="{{showDetail}}" bindtap="hideDetail">
    <view class="detail-content" catchtap="true">
      <view class="detail-header">
        <view class="detail-title">跑步详情</view>
        <view class="detail-close" bindtap="hideDetail">✕</view>
      </view>
      
      <view class="detail-info" wx:if="{{selectedRecord}}">
        <view class="detail-item">
          <text class="detail-label">日期时间:</text>
          <text class="detail-value">{{selectedRecord.date}}</text>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">跑步时长:</text>
          <text class="detail-value">{{selectedRecord.duration}}</text>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">跑步距离:</text>
          <text class="detail-value">{{selectedRecord.distance ? selectedRecord.distance.toFixed(2) : '0.00'}} 公里</text>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">消耗卡路里:</text>
          <text class="detail-value">{{selectedRecord.calories || 0}} 卡</text>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">平均配速:</text>
          <text class="detail-value">{{selectedRecord.avgPace || '00:00'}} /公里</text>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">总节拍数:</text>
          <text class="detail-value">{{selectedRecord.totalBeats || 0}} 拍</text>
        </view>
        
        <view class="detail-section">
          <view class="detail-section-title">使用设置</view>
          <view class="detail-item">
            <text class="detail-label">音色:</text>
            <text class="detail-value">{{selectedRecord.settings.voice}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">节拍:</text>
            <text class="detail-value">{{selectedRecord.settings.beat}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">BPM:</text>
            <text class="detail-value">{{selectedRecord.settings.bpm}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
