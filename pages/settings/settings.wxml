<!--settings.wxml-->
<view class="page">
  <!-- WeUI导航栏 -->
  <view class="weui-navigation-bar">
    <view class="weui-navigation-bar__inner">
      <view class="weui-navigation-bar__left">
        <navigator url="/pages/index/index" open-type="navigateBack">
          <view class="weui-navigation-bar__btn">
            <text class="nav-icon">←</text>
          </view>
        </navigator>
      </view>
      <view class="weui-navigation-bar__center">
        <view class="weui-navigation-bar__title">设置</view>
      </view>
    </view>
  </view>
  
  <!-- 音色设置 -->
  <view class="weui-panel">
    <view class="weui-panel__hd">音色设置</view>
    <view class="weui-panel__bd">
      <view class="weui-cells">
        <picker bindchange="selectVoice" value="{{selectedVoiceIndex}}" range="{{voiceTypes}}">
          <view class="weui-cell weui-cell_access">
            <view class="weui-cell__hd">
              <text class="cell-icon">🎤</text>
            </view>
            <view class="weui-cell__bd">选择音色</view>
            <view class="weui-cell__ft">{{selectedVoice}}</view>
          </view>
        </picker>
      </view>
    </view>
  </view>
  
  <!-- 节拍设置 -->
  <view class="weui-panel">
    <view class="weui-panel__hd">节拍设置</view>
    <view class="weui-panel__bd">
      <view class="weui-cells">
        <picker bindchange="selectBeat" value="{{selectedBeatIndex}}" range="{{beatTypes}}">
          <view class="weui-cell weui-cell_access">
            <view class="weui-cell__hd">
              <text class="cell-icon">🎵</text>
            </view>
            <view class="weui-cell__bd">节拍类型</view>
            <view class="weui-cell__ft">{{selectedBeat}}</view>
          </view>
        </picker>
      </view>
    </view>
  </view>
  
  <!-- BPM设置 -->
  <view class="weui-panel">
    <view class="weui-panel__hd">每分钟节拍数 (BPM)</view>
    <view class="weui-panel__bd">
      <view class="weui-cells">
        <!-- 预设选择 -->
        <picker bindchange="selectPreset" value="{{selectedPresetIndex}}" range="{{bpmPresets}}" range-key="name">
          <view class="weui-cell weui-cell_access">
            <view class="weui-cell__hd">
              <text class="cell-icon">⚡</text>
            </view>
            <view class="weui-cell__bd">快速选择</view>
            <view class="weui-cell__ft">{{bpmPresets[selectedPresetIndex].name}}</view>
          </view>
        </picker>
        
        <!-- BPM滑块 -->
        <view class="weui-cell">
          <view class="weui-cell__bd">
            <view class="weui-slider-box">
              <view class="weui-slider-label">精确调节</view>
              <view class="weui-slider">
                <slider 
                  class="weui-slider__input"
                  bindchange="onBpmChange"
                  min="{{minBpm}}"
                  max="{{maxBpm}}"
                  value="{{bpm}}"
                  step="1"
                  activeColor="#1aad19"
                  backgroundColor="#e9e9e9"
                />
              </view>
              <view class="weui-slider__value">{{bpm}} BPM</view>
            </view>
          </view>
        </view>
        
        <!-- 手动输入BPM -->
        <view class="weui-cell">
          <view class="weui-cell__hd">
            <text class="cell-icon">✏️</text>
          </view>
          <view class="weui-cell__bd">手动输入</view>
          <view class="weui-cell__ft">
            <input 
              class="weui-input"
              type="number"
              value="{{bpm}}"
              bindinput="onBpmInput"
              placeholder="输入BPM值"
            />
          </view>
        </view>
      </view>
      
      <!-- 当前BPM显示卡片 -->
      <view class="bpm-display-card">
        <view class="bpm-display-icon">
          <text class="card-icon">💓</text>
        </view>
        <view class="bpm-display-content">
          <view class="bpm-display-value">{{bpm}}</view>
          <view class="bpm-display-label">BPM</view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 操作按钮 -->
  <view class="weui-btn-area">
    <button class="weui-btn weui-btn_primary" bindtap="saveSettings">
      <text class="btn-icon">💾</text>
      保存设置
    </button>
    <button class="weui-btn weui-btn_default" bindtap="resetSettings">
      <text class="btn-icon">🔄</text>
      重置默认
    </button>
  </view>
  
  <!-- 说明信息 -->
  <view class="weui-panel">
    <view class="weui-panel__hd">使用说明</view>
    <view class="weui-panel__bd">
      <view class="weui-cells">
        <view class="weui-cell">
          <view class="weui-cell__hd">
            <text class="cell-icon">💡</text>
          </view>
          <view class="weui-cell__bd">
            <view class="info-item">超慢跑建议BPM范围：80-120</view>
            <view class="info-item">慢跑建议BPM范围：120-140</view>
            <view class="info-item">中速跑建议BPM范围：140-160</view>
            <view class="info-item">设置会自动保存到本地</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>