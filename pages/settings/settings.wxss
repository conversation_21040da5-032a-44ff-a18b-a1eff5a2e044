/**settings.wxss**/
.page {
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* WeUI导航栏样式 */
.weui-navigation-bar {
  position: relative;
  background-color: #1aad19;
  color: #fff;
  text-align: center;
}

.weui-navigation-bar__inner {
  position: relative;
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 15px;
}

.weui-navigation-bar__left,
.weui-navigation-bar__right {
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  height: 44px;
}

.weui-navigation-bar__left {
  left: 15px;
}

.weui-navigation-bar__center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
}

.weui-navigation-bar__title {
  font-size: 17px;
  font-weight: 600;
}

.weui-navigation-bar__btn {
  padding: 8px;
  color: #fff;
  font-size: 18px;
}

/* WeUI面板样式 */
.weui-panel {
  margin: 20rpx;
  border-radius: 12px;
  overflow: hidden;
}

.weui-panel__hd {
  padding: 20rpx 30rpx 15rpx;
  background-color: #fff;
  color: #333;
  font-size: 15px;
  font-weight: 600;
  border-bottom: 1rpx solid #e5e5e5;
}

/* WeUI单元格样式 */
.weui-cell {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}

.weui-cell:last-child {
  border-bottom: none;
}

.weui-cell_access {
  position: relative;
}

.weui-cell_access:after {
  content: " ";
  display: inline-block;
  height: 6px;
  width: 6px;
  border-width: 2px 2px 0 0;
  border-color: #c8c8cd;
  border-style: solid;
  transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  position: absolute;
  top: 50%;
  margin-top: -4px;
  right: 30rpx;
}

.weui-cell__hd {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.weui-cell__hd .cell-icon {
  font-size: 20px;
}

.weui-cell__bd {
  flex: 1;
  font-size: 16px;
  color: #333;
}

.weui-cell__ft {
  color: #999;
  font-size: 14px;
}

/* BPM滑块样式 */
.weui-slider-box {
  padding: 20rpx 0;
}

.weui-slider-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 15rpx;
}

.weui-slider {
  position: relative;
  padding: 0 30rpx;
}

.weui-slider__input {
  width: 100%;
}

.weui-slider__value {
  text-align: center;
  margin-top: 15rpx;
  font-size: 16px;
  font-weight: 600;
  color: #1aad19;
}

/* WeUI输入框样式 */
.weui-input {
  width: 120rpx;
  text-align: right;
  font-size: 16px;
  color: #333;
}

/* BPM显示卡片 */
.bpm-display-card {
  margin: 30rpx;
  padding: 40rpx;
  background: linear-gradient(135deg, #1aad19, #4fc3f7);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  box-shadow: 0 8rpx 32rpx rgba(26, 173, 25, 0.3);
}

.bpm-display-icon {
  margin-right: 30rpx;
  font-size: 48rpx;
}

.bmp-display-content {
  text-align: center;
}

.bpm-display-value {
  font-size: 72rpx;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 10rpx;
}

.bpm-display-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* WeUI按钮区域 */
.weui-btn-area {
  margin: 30rpx;
}

.weui-btn {
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  border-radius: 8px;
}

.weui-btn .btn-icon {
  margin-right: 10rpx;
  font-size: 18px;
}

/* 说明信息样式 */
.info-item {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10rpx;
  padding-left: 20rpx;
  position: relative;
}

.info-item:before {
  content: "•";
  position: absolute;
  left: 0;
  color: #1aad19;
  font-weight: bold;
}

.info-item:last-child {
  margin-bottom: 0;
}

/* 图标样式 */
.nav-icon, .cell-icon, .btn-icon, .card-icon {
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.nav-icon {
  font-size: 18px;
}

.cell-icon {
  font-size: 20px;
}

.btn-icon {
  font-size: 18px;
}

.card-icon {
  font-size: 48rpx;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .weui-panel {
    margin: 15rpx;
  }
  
  .bpm-display-card {
    margin: 20rpx;
    padding: 30rpx;
  }
  
  .bpm-display-value {
    font-size: 60rpx;
  }
  
  .weui-btn-area {
    margin: 20rpx;
  }
}

@media (min-width: 768px) {
  .page {
    max-width: 800rpx;
    margin: 0 auto;
  }
}