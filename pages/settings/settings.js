// settings.js
Page({
  data: {
    // 音色选项
    voiceTypes: ['男声', '女声', '机械音', '自然音'],
    selectedVoiceIndex: 0,
    selectedVoice: '男声',
    
    // 节拍类型选项
    beatTypes: ['2/4拍', '3/4拍', '4/4拍', '6/8拍'],
    selectedBeatIndex: 2, // 默认4/4拍
    selectedBeat: '4/4拍',
    
    // BPM设置
    bpm: 120,
    minBpm: 60,
    maxBpm: 200,
    
    // 预设BPM选项
    bpmPresets: [
      { name: '超慢跑', value: 100 },
      { name: '慢跑', value: 120 },
      { name: '中速跑', value: 140 },
      { name: '快跑', value: 160 },
      { name: '冲刺', value: 180 }
    ],
    selectedPresetIndex: 1 // 默认慢跑120bpm
  },
  
  onLoad: function () {
    // 页面加载时从本地存储读取设置
    this.loadSettings();
  },
  
  // 加载本地存储的设置
  loadSettings: function() {
    try {
      const voiceIndex = wx.getStorageSync('selectedVoiceIndex') || 0;
      const beatIndex = wx.getStorageSync('selectedBeatIndex') || 2;
      const bpm = wx.getStorageSync('bpm') || 120;
      const presetIndex = wx.getStorageSync('selectedPresetIndex') || 1;
      
      this.setData({
        selectedVoiceIndex: voiceIndex,
        selectedVoice: this.data.voiceTypes[voiceIndex],
        selectedBeatIndex: beatIndex,
        selectedBeat: this.data.beatTypes[beatIndex],
        bpm: bpm,
        selectedPresetIndex: presetIndex
      });
    } catch (e) {
      console.log('读取设置失败:', e);
    }
  },
  
  // 选择音色
  selectVoice: function (e) {
    const index = parseInt(e.detail.value);
    this.setData({
      selectedVoiceIndex: index,
      selectedVoice: this.data.voiceTypes[index]
    });
  },
  
  // 选择节拍类型
  selectBeat: function (e) {
    const index = parseInt(e.detail.value);
    this.setData({
      selectedBeatIndex: index,
      selectedBeat: this.data.beatTypes[index]
    });
  },
  
  // 选择预设BPM
  selectPreset: function (e) {
    const index = parseInt(e.detail.value);
    const preset = this.data.bpmPresets[index];
    this.setData({
      selectedPresetIndex: index,
      bpm: preset.value
    });
  },
  
  // BPM滑块变化
  onBpmChange: function (e) {
    this.setData({
      bpm: parseInt(e.detail.value)
    });
  },
  
  // 手动输入BPM
  onBpmInput: function (e) {
    let value = parseInt(e.detail.value);
    if (isNaN(value)) return;
    
    // 限制范围
    if (value < this.data.minBpm) value = this.data.minBpm;
    if (value > this.data.maxBpm) value = this.data.maxBpm;
    
    this.setData({
      bpm: value
    });
  },
  
  // 保存设置
  saveSettings: function () {
    try {
      // 保存到本地存储
      wx.setStorageSync('selectedVoiceIndex', this.data.selectedVoiceIndex);
      wx.setStorageSync('selectedBeatIndex', this.data.selectedBeatIndex);
      wx.setStorageSync('bpm', this.data.bpm);
      wx.setStorageSync('selectedPresetIndex', this.data.selectedPresetIndex);
      
      // 保存到全局数据
      const app = getApp();
      app.globalData.settings = {
        voice: this.data.selectedVoice,
        beat: this.data.selectedBeat,
        bpm: this.data.bpm
      };
      
      wx.showToast({
        title: '设置已保存',
        icon: 'success',
        duration: 2000
      });
    } catch (e) {
      wx.showToast({
        title: '保存失败',
        icon: 'error',
        duration: 2000
      });
      console.log('保存设置失败:', e);
    }
  },
  
  // 重置设置
  resetSettings: function () {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有设置为默认值吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            selectedVoiceIndex: 0,
            selectedVoice: '男声',
            selectedBeatIndex: 2,
            selectedBeat: '4/4拍',
            bpm: 120,
            selectedPresetIndex: 1
          });
          
          // 清除本地存储
          wx.removeStorageSync('selectedVoiceIndex');
          wx.removeStorageSync('selectedBeatIndex');
          wx.removeStorageSync('bpm');
          wx.removeStorageSync('selectedPresetIndex');
          
          wx.showToast({
            title: '已重置',
            icon: 'success',
            duration: 2000
          });
        }
      }
    });
  }
})
