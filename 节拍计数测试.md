# 节拍计数功能测试指南

## 🐛 问题描述

**原问题**: 总节拍数一直显示为0，即使跑步过程中有节拍播放。

**问题原因**: 
1. 在暂停跑步时，`cleanup()` 函数调用了 `metronome.reset()`，重置了节拍计数
2. 暂停后继续跑步时，节拍器重新开始计数，但页面显示的 `totalBeats` 没有正确更新
3. 节拍器的 `totalBeats` 和页面的 `totalBeats` 没有正确同步

## ✅ 修复方案

### 1. 分离停止和重置操作
```javascript
// 暂停时：只停止节拍器，不重置计数
pauseTimer: function () {
  metronome.stop(); // 只停止，不重置
  const currentTotalBeats = metronome.getStatus().totalBeats;
  this.setData({ totalBeats: currentTotalBeats }); // 保存当前计数
}

// 重置时：明确重置节拍器
resetTimer: function () {
  metronome.reset(); // 明确重置
}
```

### 2. 添加节拍计数恢复功能
```javascript
// 在节拍器中添加设置初始计数的方法
setTotalBeats(count) {
  this.totalBeats = count || 0;
}

// 继续跑步时恢复计数
startTimer: function () {
  if (this.data.pausedTime > 0) {
    metronome.setTotalBeats(this.data.totalBeats); // 恢复计数
  }
  metronome.start();
}
```

### 3. 确保回调函数正确更新
```javascript
// 节拍器回调正确更新页面数据
metronome.setCallbacks({
  onBeat: (beat, isAccent, totalBeats) => {
    this.setData({
      currentBeatInMeasure: beat,
      totalBeats: totalBeats // 实时更新总节拍数
    });
  }
});
```

## 🧪 测试步骤

### 测试用例 1: 基本节拍计数
1. **开始跑步** - 点击"开始"按钮
2. **观察节拍** - 等待10秒，观察节拍指示器和震动
3. **检查计数** - 在120 BPM下，10秒应该有约20拍
4. **验证显示** - 总节拍数应该显示约20

**预期结果**: 
- ✅ 节拍指示器正常闪烁
- ✅ 总节拍数实时增加
- ✅ 数值与时间和BPM匹配

### 测试用例 2: 暂停继续节拍计数
1. **开始跑步** - 运行10秒，记录节拍数（约20拍）
2. **暂停跑步** - 点击暂停，节拍数应保持不变
3. **继续跑步** - 点击开始，节拍应从之前的数值继续
4. **再运行10秒** - 总节拍数应该约为40拍

**预期结果**:
- ✅ 暂停时节拍数保持不变
- ✅ 继续时节拍数从暂停点继续
- ✅ 总计数正确累加

### 测试用例 3: 多次暂停继续
1. **第一段** - 运行5秒，暂停（约10拍）
2. **第二段** - 继续运行5秒，暂停（约20拍）
3. **第三段** - 继续运行10秒（约40拍）
4. **验证总数** - 最终应显示约40拍

**预期结果**:
- ✅ 每次暂停后计数保持
- ✅ 每次继续后计数累加
- ✅ 最终总数正确

### 测试用例 4: 重置功能
1. **运行一段时间** - 累积一些节拍数
2. **重置** - 点击重置按钮
3. **重新开始** - 点击开始按钮
4. **验证重置** - 节拍数应从0开始

**预期结果**:
- ✅ 重置后节拍数归零
- ✅ 重新开始时从0计数
- ✅ 不受之前运行影响

## 📊 节拍数计算验证

### 理论计算
```javascript
// BPM = 120，即每分钟120拍
// 每秒节拍数 = 120 / 60 = 2拍/秒
// 10秒预期节拍数 = 10 * 2 = 20拍

function calculateExpectedBeats(seconds, bpm) {
  return Math.floor(seconds * (bpm / 60));
}

// 验证示例
console.log('10秒@120BPM预期:', calculateExpectedBeats(10, 120)); // 20拍
console.log('30秒@100BPM预期:', calculateExpectedBeats(30, 100)); // 50拍
```

### 实际验证
```javascript
// 在控制台查看节拍器状态
console.log('节拍器状态:', metronome.getStatus());
console.log('页面节拍数:', this.data.totalBeats);

// 验证同步
const metronomeBeats = metronome.getStatus().totalBeats;
const pageBeats = this.data.totalBeats;
console.log('同步检查:', metronomeBeats === pageBeats);
```

## 🔍 调试信息

修复后的代码会输出以下调试信息：

```
开始跑步，累计时间: 0 ms
设置节拍器初始计数: 0
节拍器启动: 120 BPM, 4/4拍
暂停跑步，本次运行时间: 10000 ms，累计时间: 10000 ms，总节拍数: 20
开始跑步，累计时间: 10000 ms
设置节拍器初始计数: 20
节拍器启动: 120 BPM, 4/4拍
```

## 🎯 不同BPM的预期节拍数

| BPM | 每秒节拍数 | 10秒节拍数 | 30秒节拍数 | 60秒节拍数 |
|-----|-----------|-----------|-----------|-----------|
| 60  | 1.0       | 10        | 30        | 60        |
| 100 | 1.67      | 17        | 50        | 100       |
| 120 | 2.0       | 20        | 60        | 120       |
| 150 | 2.5       | 25        | 75        | 150       |
| 180 | 3.0       | 30        | 90        | 180       |

## ✅ 验证清单

### 功能验证
- [ ] 开始跑步时节拍数从0开始计数
- [ ] 节拍数实时增加，与时间和BPM匹配
- [ ] 暂停时节拍数停止增加
- [ ] 继续时节拍数从暂停点继续
- [ ] 多次暂停继续时节拍数正确累加
- [ ] 重置时节拍数归零

### 数据验证
- [ ] 节拍数与理论计算值接近（±2拍误差可接受）
- [ ] 页面显示的节拍数与节拍器内部计数同步
- [ ] 完成跑步时节拍数正确保存到历史记录
- [ ] 节拍指示器与节拍计数同步

### 边界情况验证
- [ ] 快速暂停继续时计数正确
- [ ] 切换BPM后计数继续正常
- [ ] 关闭节拍后重新开启计数正确
- [ ] 应用被强制关闭后恢复计数

## 📝 使用建议

1. **观察节拍指示器**: 圆圈闪烁表示节拍正常工作
2. **检查总节拍数**: 应该与跑步时间和BPM设置匹配
3. **验证重音节拍**: 每小节第一拍有不同的震动强度
4. **使用暂停功能**: 可以随时暂停，节拍数会正确保持

---

**修复状态**: ✅ 已完成
**测试状态**: 🧪 待验证
**影响范围**: 节拍计数、跑步统计、历史记录