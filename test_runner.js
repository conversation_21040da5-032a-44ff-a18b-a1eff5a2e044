// test_runner.js - 超慢跑助手小程序测试脚本
// 这个脚本可以在微信开发者工具的控制台中运行来测试核心功能

const TestRunner = {
  // 测试结果记录
  results: {
    passed: 0,
    failed: 0,
    tests: []
  },

  // 断言函数
  assert: function(condition, message) {
    if (condition) {
      this.results.passed++;
      this.results.tests.push({ status: 'PASS', message });
      console.log(`✅ PASS: ${message}`);
    } else {
      this.results.failed++;
      this.results.tests.push({ status: 'FAIL', message });
      console.log(`❌ FAIL: ${message}`);
    }
  },

  // 等待函数
  wait: function(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  // 测试本地存储功能
  testLocalStorage: function() {
    console.log('\n🧪 测试本地存储功能...');
    
    try {
      // 测试设置存储
      const testSettings = {
        selectedVoiceIndex: 1,
        selectedBeatIndex: 0,
        bpm: 150,
        selectedPresetIndex: 2
      };

      // 保存测试数据
      wx.setStorageSync('test_selectedVoiceIndex', testSettings.selectedVoiceIndex);
      wx.setStorageSync('test_selectedBeatIndex', testSettings.selectedBeatIndex);
      wx.setStorageSync('test_bpm', testSettings.bpm);
      wx.setStorageSync('test_selectedPresetIndex', testSettings.selectedPresetIndex);

      // 读取并验证
      const voiceIndex = wx.getStorageSync('test_selectedVoiceIndex');
      const beatIndex = wx.getStorageSync('test_selectedBeatIndex');
      const bpm = wx.getStorageSync('test_bpm');
      const presetIndex = wx.getStorageSync('test_selectedPresetIndex');

      this.assert(voiceIndex === testSettings.selectedVoiceIndex, '音色索引存储和读取正确');
      this.assert(beatIndex === testSettings.selectedBeatIndex, '节拍索引存储和读取正确');
      this.assert(bpm === testSettings.bpm, 'BPM值存储和读取正确');
      this.assert(presetIndex === testSettings.selectedPresetIndex, '预设索引存储和读取正确');

      // 清理测试数据
      wx.removeStorageSync('test_selectedVoiceIndex');
      wx.removeStorageSync('test_selectedBeatIndex');
      wx.removeStorageSync('test_bpm');
      wx.removeStorageSync('test_selectedPresetIndex');

      this.assert(wx.getStorageSync('test_selectedVoiceIndex') === '', '测试数据清理成功');

    } catch (e) {
      this.assert(false, `本地存储测试失败: ${e.message}`);
    }
  },

  // 测试跑步记录功能
  testRunRecords: function() {
    console.log('\n🧪 测试跑步记录功能...');
    
    try {
      // 创建测试记录
      const testRecord = {
        id: Date.now(),
        date: new Date().toISOString(),
        duration: '00:15:30',
        distance: 2.5,
        calories: 150,
        avgPace: '06:12',
        totalBeats: 1860,
        settings: {
          voice: '女声',
          beat: '4/4拍',
          bpm: 120
        }
      };

      // 保存记录
      let records = wx.getStorageSync('runRecords') || [];
      records.unshift(testRecord);
      wx.setStorageSync('runRecords', records);

      // 读取并验证
      const savedRecords = wx.getStorageSync('runRecords') || [];
      this.assert(savedRecords.length > 0, '跑步记录保存成功');
      
      const savedRecord = savedRecords[0];
      this.assert(savedRecord.id === testRecord.id, '记录ID正确');
      this.assert(savedRecord.duration === testRecord.duration, '跑步时长正确');
      this.assert(savedRecord.distance === testRecord.distance, '跑步距离正确');
      this.assert(savedRecord.calories === testRecord.calories, '消耗卡路里正确');
      this.assert(savedRecord.settings.bpm === testRecord.settings.bpm, '设置信息正确');

      // 清理测试数据
      const cleanRecords = savedRecords.filter(record => record.id !== testRecord.id);
      wx.setStorageSync('runRecords', cleanRecords);

    } catch (e) {
      this.assert(false, `跑步记录测试失败: ${e.message}`);
    }
  },

  // 测试时间格式化功能
  testTimeFormatting: function() {
    console.log('\n🧪 测试时间格式化功能...');
    
    // 模拟时间格式化函数
    const formatTime = function(milliseconds) {
      const seconds = Math.floor(milliseconds / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);
      const pad = (num) => num < 10 ? `0${num}` : num;
      return `${pad(hours)}:${pad(minutes % 60)}:${pad(seconds % 60)}`;
    };

    const timeToSeconds = function(timeStr) {
      const parts = timeStr.split(':');
      return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2]);
    };

    // 测试用例
    this.assert(formatTime(0) === '00:00:00', '零时间格式化正确');
    this.assert(formatTime(1000) === '00:00:01', '1秒格式化正确');
    this.assert(formatTime(60000) === '00:01:00', '1分钟格式化正确');
    this.assert(formatTime(3600000) === '01:00:00', '1小时格式化正确');
    this.assert(formatTime(3661000) === '01:01:01', '复合时间格式化正确');

    this.assert(timeToSeconds('00:00:00') === 0, '零时间转换正确');
    this.assert(timeToSeconds('00:00:01') === 1, '1秒转换正确');
    this.assert(timeToSeconds('00:01:00') === 60, '1分钟转换正确');
    this.assert(timeToSeconds('01:00:00') === 3600, '1小时转换正确');
    this.assert(timeToSeconds('01:01:01') === 3661, '复合时间转换正确');
  },

  // 测试BPM计算功能
  testBPMCalculation: function() {
    console.log('\n🧪 测试BPM计算功能...');
    
    // 模拟BPM相关计算
    const calculateBeatInterval = function(bpm) {
      return 60000 / bpm; // 毫秒
    };

    const validateBPMRange = function(bpm, min = 60, max = 200) {
      return bpm >= min && bpm <= max;
    };

    // 测试用例
    this.assert(calculateBeatInterval(60) === 1000, '60 BPM间隔计算正确');
    this.assert(calculateBeatInterval(120) === 500, '120 BPM间隔计算正确');
    this.assert(calculateBeatInterval(180) === 333.3333333333333, '180 BPM间隔计算正确');

    this.assert(validateBPMRange(120), '有效BPM范围验证正确');
    this.assert(!validateBPMRange(50), '低于最小BPM验证正确');
    this.assert(!validateBPMRange(250), '高于最大BPM验证正确');
    this.assert(validateBPMRange(60), '边界BPM验证正确');
    this.assert(validateBPMRange(200), '边界BPM验证正确');
  },

  // 测试统计计算功能
  testStatisticsCalculation: function() {
    console.log('\n🧪 测试统计计算功能...');
    
    // 模拟统计数据
    const mockRecords = [
      { duration: '00:30:00', distance: 4.0, calories: 200 },
      { duration: '00:45:00', distance: 6.0, calories: 300 },
      { duration: '00:20:00', distance: 2.5, calories: 125 }
    ];

    const timeToSeconds = function(timeStr) {
      const parts = timeStr.split(':');
      return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2]);
    };

    const secondsToTime = function(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      const pad = (num) => num < 10 ? `0${num}` : num;
      return `${pad(hours)}:${pad(minutes)}:${pad(secs)}`;
    };

    // 计算统计数据
    let totalSeconds = 0;
    let totalDistance = 0;
    let totalCalories = 0;

    mockRecords.forEach(record => {
      totalSeconds += timeToSeconds(record.duration);
      totalDistance += record.distance;
      totalCalories += record.calories;
    });

    this.assert(mockRecords.length === 3, '记录数量统计正确');
    this.assert(secondsToTime(totalSeconds) === '01:35:00', '总时间计算正确');
    this.assert(totalDistance === 12.5, '总距离计算正确');
    this.assert(totalCalories === 625, '总卡路里计算正确');

    const avgPace = totalDistance > 0 ? (totalSeconds / 60) / totalDistance : 0;
    this.assert(Math.abs(avgPace - 7.6) < 0.1, '平均配速计算正确');
  },

  // 测试数据验证功能
  testDataValidation: function() {
    console.log('\n🧪 测试数据验证功能...');
    
    // 模拟验证函数
    const validateSettings = function(settings) {
      const voiceTypes = ['男声', '女声', '机械音', '自然音'];
      const beatTypes = ['2/4拍', '3/4拍', '4/4拍', '6/8拍'];
      
      return (
        voiceTypes.includes(settings.voice) &&
        beatTypes.includes(settings.beat) &&
        settings.bpm >= 60 &&
        settings.bpm <= 200
      );
    };

    // 测试用例
    const validSettings = { voice: '男声', beat: '4/4拍', bpm: 120 };
    const invalidVoice = { voice: '无效音色', beat: '4/4拍', bpm: 120 };
    const invalidBeat = { voice: '男声', beat: '无效节拍', bpm: 120 };
    const invalidBPM = { voice: '男声', beat: '4/4拍', bpm: 300 };

    this.assert(validateSettings(validSettings), '有效设置验证通过');
    this.assert(!validateSettings(invalidVoice), '无效音色验证失败');
    this.assert(!validateSettings(invalidBeat), '无效节拍验证失败');
    this.assert(!validateSettings(invalidBPM), '无效BPM验证失败');
  },

  // 运行所有测试
  runAllTests: function() {
    console.log('🚀 开始运行超慢跑助手小程序测试套件...\n');
    
    // 重置结果
    this.results = { passed: 0, failed: 0, tests: [] };

    // 运行测试
    this.testLocalStorage();
    this.testRunRecords();
    this.testTimeFormatting();
    this.testBPMCalculation();
    this.testStatisticsCalculation();
    this.testDataValidation();

    // 输出结果
    console.log('\n📊 测试结果汇总:');
    console.log(`✅ 通过: ${this.results.passed}`);
    console.log(`❌ 失败: ${this.results.failed}`);
    console.log(`📈 通过率: ${((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)}%`);

    if (this.results.failed === 0) {
      console.log('\n🎉 所有测试通过！应用核心功能正常。');
    } else {
      console.log('\n⚠️  有测试失败，请检查相关功能。');
    }

    return this.results;
  },

  // 生成测试报告
  generateReport: function() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.results.passed + this.results.failed,
        passed: this.results.passed,
        failed: this.results.failed,
        passRate: ((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)
      },
      details: this.results.tests
    };

    console.log('\n📋 详细测试报告:');
    console.log(JSON.stringify(report, null, 2));

    return report;
  }
};

// 导出测试运行器（在开发者工具控制台中使用）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TestRunner;
} else {
  // 在浏览器环境中直接运行
  window.TestRunner = TestRunner;
}

// 使用说明
console.log(`
🧪 超慢跑助手测试运行器已加载

使用方法:
1. 在微信开发者工具控制台中运行: TestRunner.runAllTests()
2. 查看详细报告: TestRunner.generateReport()
3. 运行单个测试: TestRunner.testLocalStorage() 等

可用的测试方法:
- testLocalStorage(): 测试本地存储
- testRunRecords(): 测试跑步记录
- testTimeFormatting(): 测试时间格式化
- testBPMCalculation(): 测试BPM计算
- testStatisticsCalculation(): 测试统计计算
- testDataValidation(): 测试数据验证
`);