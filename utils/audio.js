// utils/audio.js - 音频管理工具
class AudioManager {
  constructor() {
    this.audioContext = null;
    this.isInitialized = false;
    this.currentVoice = '男声';
    this.volume = 0.8;
    this.isEnabled = true;
    
    // 音频缓存
    this.audioCache = new Map();
    
    // 初始化音频
    this.init();
  }

  // 初始化音频系统
  init() {
    try {
      // 创建音频上下文
      this.audioContext = wx.createInnerAudioContext();
      this.audioContext.volume = this.volume;
      this.isInitialized = true;
      
      console.log('音频系统初始化成功');
    } catch (error) {
      console.error('音频系统初始化失败:', error);
      this.isInitialized = false;
    }
  }

  // 设置音色
  setVoice(voice) {
    this.currentVoice = voice;
  }

  // 设置音量
  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
    if (this.audioContext) {
      this.audioContext.volume = this.volume;
    }
  }

  // 启用/禁用音频
  setEnabled(enabled) {
    this.isEnabled = enabled;
  }

  // 播放节拍音
  playBeat(isAccent = false, beatType = '4/4拍') {
    if (!this.isEnabled || !this.isInitialized) {
      return this.playVibration(isAccent);
    }

    try {
      // 根据音色和重音播放不同的音效
      const soundType = this.getSoundType(isAccent, beatType);
      this.playSound(soundType);
      
      // 同时播放震动反馈
      this.playVibration(isAccent);
    } catch (error) {
      console.error('播放节拍音失败:', error);
      // 降级到震动
      this.playVibration(isAccent);
    }
  }

  // 获取音效类型
  getSoundType(isAccent, beatType) {
    const baseType = isAccent ? 'accent' : 'normal';
    return `${this.currentVoice}_${baseType}`;
  }

  // 播放音效
  playSound(soundType) {
    try {
      // 根据音色播放不同的音效
      switch (this.currentVoice) {
        case '男声':
          this.playTone(soundType.includes('accent') ? 400 : 300, 100);
          break;
        case '女声':
          this.playTone(soundType.includes('accent') ? 800 : 600, 100);
          break;
        case '机械音':
          this.playTone(1000, 50);
          break;
        case '自然音':
          this.playTone(soundType.includes('accent') ? 1200 : 800, 80);
          break;
        default:
          this.playTone(600, 100);
      }
    } catch (error) {
      console.error('播放音效失败:', error);
    }
  },

  // 播放音调
  playTone(frequency, duration) {
    try {
      // 创建音频上下文
      if (!this.webAudioContext) {
        // 在小程序中，我们使用简单的提示音
        this.playSimpleBeep(frequency);
        return;
      }
      
      const oscillator = this.webAudioContext.createOscillator();
      const gainNode = this.webAudioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(this.webAudioContext.destination);
      
      oscillator.frequency.setValueAtTime(frequency, this.webAudioContext.currentTime);
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(this.volume * 0.3, this.webAudioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, this.webAudioContext.currentTime + duration / 1000);
      
      oscillator.start(this.webAudioContext.currentTime);
      oscillator.stop(this.webAudioContext.currentTime + duration / 1000);
      
      console.log(`播放音调: ${frequency}Hz, 时长: ${duration}ms`);
    } catch (error) {
      console.error('播放音调失败:', error);
      // 降级到简单提示音
      this.playSimpleBeep(frequency);
    }
  },

  // 播放简单提示音
  playSimpleBeep(frequency) {
    try {
      // 在小程序中，我们可以使用内置的音频播放
      console.log(`播放简单提示音: ${frequency}Hz`);
      
      // 创建一个简单的音频文件URL（data URI）
      const audioData = this.generateSimpleBeep(frequency);
      
      if (this.audioContext) {
        this.audioContext.src = audioData;
        this.audioContext.play();
      }
    } catch (error) {
      console.error('播放简单提示音失败:', error);
    }
  },

  // 生成简单的提示音数据
  generateSimpleBeep(frequency) {
    try {
      // 生成一个简单的正弦波音频数据
      const sampleRate = 8000;
      const duration = 0.1; // 100ms
      const samples = Math.floor(sampleRate * duration);
      const buffer = new ArrayBuffer(44 + samples * 2);
      const view = new DataView(buffer);
      
      // WAV文件头
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };
      
      writeString(0, 'RIFF');
      view.setUint32(4, 36 + samples * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, 1, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * 2, true);
      view.setUint16(32, 2, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, samples * 2, true);
      
      // 生成音频数据
      for (let i = 0; i < samples; i++) {
        const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.3;
        view.setInt16(44 + i * 2, sample * 32767, true);
      }
      
      // 转换为data URI
      const blob = new Blob([buffer], { type: 'audio/wav' });
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error('生成音频数据失败:', error);
      return '';
    }
  },

  // 播放震动反馈
  playVibration(isAccent = false) {
    try {
      if (isAccent) {
        // 重音节拍 - 强震动（使用长震动）
        wx.vibrateLong({
          success: () => console.log('重音震动播放成功'),
          fail: (error) => {
            console.error('长震动播放失败，尝试短震动:', error);
            // 降级到短震动
            wx.vibrateShort({
              success: () => console.log('重音震动播放成功（降级）'),
              fail: (err) => console.error('震动播放完全失败:', err)
            });
          }
        });
      } else {
        // 普通节拍 - 轻震动
        wx.vibrateShort({
          success: () => console.log('普通震动播放成功'),
          fail: (error) => {
            console.error('普通震动播放失败:', error);
            // 如果震动失败，可以尝试其他反馈方式
            this.playAlternativeFeedback();
          }
        });
      }
    } catch (error) {
      console.error('震动播放失败:', error);
      this.playAlternativeFeedback();
    }
  },

  // 替代反馈方式
  playAlternativeFeedback() {
    try {
      // 如果震动不可用，可以尝试其他反馈方式
      console.log('使用替代反馈方式');
      // 这里可以添加其他反馈方式，比如屏幕闪烁等
    } catch (error) {
      console.error('替代反馈播放失败:', error);
    }
  },

  // 播放提示音
  playNotification(type = 'success') {
    if (!this.isEnabled) return;

    try {
      switch (type) {
        case 'success':
          this.playSuccessSound();
          break;
        case 'warning':
          this.playWarningSound();
          break;
        case 'error':
          this.playErrorSound();
          break;
        case 'start':
          this.playStartSound();
          break;
        case 'pause':
          this.playPauseSound();
          break;
        case 'finish':
          this.playFinishSound();
          break;
        default:
          this.playDefaultSound();
      }
    } catch (error) {
      console.error('播放提示音失败:', error);
    }
  }

  // 播放成功音效
  playSuccessSound() {
    console.log('播放成功音效');
    wx.vibrateShort({
      success: () => console.log('成功音效震动播放成功'),
      fail: (error) => console.error('成功音效震动播放失败:', error)
    });
  },

  // 播放警告音效
  playWarningSound() {
    console.log('播放警告音效');
    wx.vibrateLong({
      success: () => console.log('警告音效震动播放成功'),
      fail: (error) => {
        console.error('警告音效长震动失败，尝试短震动:', error);
        wx.vibrateShort();
      }
    });
  },

  // 播放错误音效
  playErrorSound() {
    console.log('播放错误音效');
    wx.vibrateLong({
      success: () => {
        setTimeout(() => {
          wx.vibrateShort();
        }, 300);
      },
      fail: (error) => {
        console.error('错误音效震动失败:', error);
        wx.vibrateShort();
      }
    });
  },

  // 播放开始音效
  playStartSound() {
    console.log('播放开始音效');
    wx.vibrateShort({
      success: () => {
        setTimeout(() => {
          wx.vibrateShort();
        }, 150);
        setTimeout(() => {
          wx.vibrateLong();
        }, 300);
      },
      fail: (error) => console.error('开始音效震动失败:', error)
    });
  },

  // 播放暂停音效
  playPauseSound() {
    console.log('播放暂停音效');
    wx.vibrateShort({
      success: () => {
        setTimeout(() => {
          wx.vibrateShort();
        }, 200);
      },
      fail: (error) => console.error('暂停音效震动失败:', error)
    });
  },

  // 播放完成音效
  playFinishSound() {
    console.log('播放完成音效');
    const pattern = [0, 150, 300, 450, 600];
    
    pattern.forEach((delay, index) => {
      setTimeout(() => {
        if (index === pattern.length - 1) {
          // 最后一次使用长震动
          wx.vibrateLong({
            fail: () => wx.vibrateShort()
          });
        } else {
          wx.vibrateShort({
            fail: (error) => console.error(`完成音效第${index + 1}次震动失败:`, error)
          });
        }
      }, delay);
    });
  },

  // 播放默认音效
  playDefaultSound() {
    console.log('播放默认音效');
    wx.vibrateShort({
      success: () => console.log('默认音效震动播放成功'),
      fail: (error) => console.error('默认音效震动播放失败:', error)
    });
  },

  // 预加载音频资源
  preloadAudio() {
    // 在实际应用中，这里会预加载音频文件
    console.log('预加载音频资源');
    
    const audioFiles = [
      'male_accent.mp3',
      'male_normal.mp3',
      'female_accent.mp3',
      'female_normal.mp3',
      'mechanical_beep.mp3',
      'natural_tick.mp3',
      'natural_tock.mp3'
    ];

    audioFiles.forEach(file => {
      // 模拟预加载
      console.log(`预加载音频: ${file}`);
    });
  }

  // 测试音效
  testAudio() {
    console.log('开始音效测试...');
    
    // 测试不同类型的音效
    setTimeout(() => this.playBeat(true, '4/4拍'), 0);
    setTimeout(() => this.playBeat(false, '4/4拍'), 500);
    setTimeout(() => this.playNotification('start'), 1000);
    setTimeout(() => this.playNotification('success'), 1500);
    setTimeout(() => this.playNotification('finish'), 2000);
    
    console.log('音效测试完成');
  }

  // 销毁音频系统
  destroy() {
    try {
      if (this.audioContext) {
        this.audioContext.destroy();
        this.audioContext = null;
      }
      
      this.audioCache.clear();
      this.isInitialized = false;
      
      console.log('音频系统已销毁');
    } catch (error) {
      console.error('销毁音频系统失败:', error);
    }
  }

  // 获取音频状态
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isEnabled: this.isEnabled,
      currentVoice: this.currentVoice,
      volume: this.volume
    };
  }
}

// 创建全局音频管理器实例
const audioManager = new AudioManager();

module.exports = {
  AudioManager,
  audioManager
};