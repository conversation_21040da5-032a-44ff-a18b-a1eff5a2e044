// utils/simple-audio.js - 简化的音频管理工具
class SimpleAudioManager {
  constructor() {
    this.isEnabled = true;
    this.currentVoice = '男声';
    this.volume = 0.8;
    
    // 音频上下文
    this.audioContexts = [];
    this.maxContexts = 3; // 限制同时播放的音频数量
    
    this.init();
  }

  // 初始化
  init() {
    try {
      console.log('简化音频系统初始化');
      this.isEnabled = true;
    } catch (error) {
      console.error('音频系统初始化失败:', error);
      this.isEnabled = false;
    }
  }

  // 设置音色
  setVoice(voice) {
    this.currentVoice = voice;
    console.log(`设置音色: ${voice}`);
  }

  // 设置音量
  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
    console.log(`设置音量: ${this.volume}`);
  }

  // 启用/禁用音频
  setEnabled(enabled) {
    this.isEnabled = enabled;
    console.log(`音频${enabled ? '启用' : '禁用'}`);
  }

  // 播放节拍音
  playBeat(isAccent = false, beatType = '4/4拍') {
    if (!this.isEnabled) {
      return this.playVibration(isAccent);
    }

    try {
      // 同时播放音效和震动
      this.playBeatSound(isAccent);
      this.playVibration(isAccent);
    } catch (error) {
      console.error('播放节拍音失败:', error);
      // 降级到仅震动
      this.playVibration(isAccent);
    }
  }

  // 播放节拍声音
  playBeatSound(isAccent) {
    try {
      // 根据音色选择不同的频率
      let frequency;
      switch (this.currentVoice) {
        case '男声':
          frequency = isAccent ? 400 : 300;
          break;
        case '女声':
          frequency = isAccent ? 800 : 600;
          break;
        case '机械音':
          frequency = 1000;
          break;
        case '自然音':
          frequency = isAccent ? 1200 : 800;
          break;
        default:
          frequency = 600;
      }

      // 使用微信小程序的音频API
      this.playToneWithWxAudio(frequency, isAccent ? 150 : 100);
      
    } catch (error) {
      console.error('播放节拍声音失败:', error);
    }
  }

  // 使用微信音频API播放音调
  playToneWithWxAudio(frequency, duration) {
    try {
      // 清理旧的音频上下文
      this.cleanupAudioContexts();
      
      // 创建新的音频上下文
      const audioContext = wx.createInnerAudioContext();
      
      // 设置音频属性
      audioContext.volume = this.volume;
      audioContext.loop = false;
      
      // 生成音频数据URL
      const audioUrl = this.generateToneDataUrl(frequency, duration);
      
      if (audioUrl) {
        audioContext.src = audioUrl;
        
        // 播放音频
        audioContext.play();
        
        // 添加到管理列表
        this.audioContexts.push(audioContext);
        
        // 设置自动清理
        audioContext.onEnded(() => {
          this.removeAudioContext(audioContext);
        });
        
        audioContext.onError((error) => {
          console.error('音频播放错误:', error);
          this.removeAudioContext(audioContext);
        });
        
        // 设置超时清理
        setTimeout(() => {
          this.removeAudioContext(audioContext);
        }, duration + 100);
        
        console.log(`播放音调: ${frequency}Hz, 时长: ${duration}ms`);
      }
      
    } catch (error) {
      console.error('使用微信音频API播放失败:', error);
    }
  }

  // 生成音调数据URL
  generateToneDataUrl(frequency, duration) {
    try {
      // 简化的音频数据生成
      // 由于小程序限制，这里返回一个静态的音频文件URL
      // 在实际应用中，应该预先准备好音频文件
      
      console.log(`生成音调数据: ${frequency}Hz, ${duration}ms`);
      
      // 返回空字符串，表示使用震动替代
      return '';
      
    } catch (error) {
      console.error('生成音调数据失败:', error);
      return '';
    }
  }

  // 播放震动反馈
  playVibration(isAccent = false) {
    try {
      if (isAccent) {
        // 重音节拍 - 长震动
        wx.vibrateLong({
          success: () => console.log('重音震动播放成功'),
          fail: (error) => {
            console.error('长震动失败，尝试短震动:', error);
            wx.vibrateShort({
              success: () => console.log('重音震动播放成功（降级）'),
              fail: (err) => console.error('震动播放完全失败:', err)
            });
          }
        });
      } else {
        // 普通节拍 - 短震动
        wx.vibrateShort({
          success: () => console.log('普通震动播放成功'),
          fail: (error) => {
            console.error('普通震动播放失败:', error);
          }
        });
      }
    } catch (error) {
      console.error('震动播放失败:', error);
    }
  }

  // 播放提示音
  playNotification(type = 'success') {
    if (!this.isEnabled) return;

    try {
      switch (type) {
        case 'success':
          this.playSuccessSound();
          break;
        case 'warning':
          this.playWarningSound();
          break;
        case 'error':
          this.playErrorSound();
          break;
        case 'start':
          this.playStartSound();
          break;
        case 'pause':
          this.playPauseSound();
          break;
        case 'finish':
          this.playFinishSound();
          break;
        default:
          this.playDefaultSound();
      }
    } catch (error) {
      console.error('播放提示音失败:', error);
    }
  }

  // 播放成功音效
  playSuccessSound() {
    console.log('播放成功音效');
    wx.vibrateShort({
      success: () => console.log('成功音效震动播放成功'),
      fail: (error) => console.error('成功音效震动播放失败:', error)
    });
  }

  // 播放警告音效
  playWarningSound() {
    console.log('播放警告音效');
    wx.vibrateLong({
      success: () => console.log('警告音效震动播放成功'),
      fail: (error) => {
        console.error('警告音效长震动失败，尝试短震动:', error);
        wx.vibrateShort();
      }
    });
  }

  // 播放错误音效
  playErrorSound() {
    console.log('播放错误音效');
    wx.vibrateLong({
      success: () => {
        setTimeout(() => {
          wx.vibrateShort();
        }, 300);
      },
      fail: (error) => {
        console.error('错误音效震动失败:', error);
        wx.vibrateShort();
      }
    });
  }

  // 播放开始音效
  playStartSound() {
    console.log('播放开始音效');
    wx.vibrateShort({
      success: () => {
        setTimeout(() => {
          wx.vibrateShort();
        }, 150);
        setTimeout(() => {
          wx.vibrateLong();
        }, 300);
      },
      fail: (error) => console.error('开始音效震动失败:', error)
    });
  }

  // 播放暂停音效
  playPauseSound() {
    console.log('播放暂停音效');
    wx.vibrateShort({
      success: () => {
        setTimeout(() => {
          wx.vibrateShort();
        }, 200);
      },
      fail: (error) => console.error('暂停音效震动失败:', error)
    });
  }

  // 播放完成音效
  playFinishSound() {
    console.log('播放完成音效');
    const pattern = [0, 150, 300, 450, 600];
    
    pattern.forEach((delay, index) => {
      setTimeout(() => {
        if (index === pattern.length - 1) {
          wx.vibrateLong({
            fail: () => wx.vibrateShort()
          });
        } else {
          wx.vibrateShort({
            fail: (error) => console.error(`完成音效第${index + 1}次震动失败:`, error)
          });
        }
      }, delay);
    });
  }

  // 播放默认音效
  playDefaultSound() {
    console.log('播放默认音效');
    wx.vibrateShort({
      success: () => console.log('默认音效震动播放成功'),
      fail: (error) => console.error('默认音效震动播放失败:', error)
    });
  }

  // 测试音效
  testAudio() {
    console.log('开始音效测试...');
    
    setTimeout(() => this.playBeat(true, '4/4拍'), 0);
    setTimeout(() => this.playBeat(false, '4/4拍'), 500);
    setTimeout(() => this.playNotification('start'), 1000);
    setTimeout(() => this.playNotification('success'), 1500);
    setTimeout(() => this.playNotification('finish'), 2000);
    
    console.log('音效测试完成');
  }

  // 清理音频上下文
  cleanupAudioContexts() {
    // 如果音频上下文太多，清理旧的
    while (this.audioContexts.length >= this.maxContexts) {
      const oldContext = this.audioContexts.shift();
      this.destroyAudioContext(oldContext);
    }
  }

  // 移除音频上下文
  removeAudioContext(audioContext) {
    const index = this.audioContexts.indexOf(audioContext);
    if (index > -1) {
      this.audioContexts.splice(index, 1);
      this.destroyAudioContext(audioContext);
    }
  }

  // 销毁音频上下文
  destroyAudioContext(audioContext) {
    try {
      if (audioContext) {
        audioContext.destroy();
      }
    } catch (error) {
      console.error('销毁音频上下文失败:', error);
    }
  }

  // 销毁音频系统
  destroy() {
    try {
      // 清理所有音频上下文
      this.audioContexts.forEach(context => {
        this.destroyAudioContext(context);
      });
      this.audioContexts = [];
      
      this.isEnabled = false;
      console.log('简化音频系统已销毁');
    } catch (error) {
      console.error('销毁音频系统失败:', error);
    }
  }

  // 获取音频状态
  getStatus() {
    return {
      isEnabled: this.isEnabled,
      currentVoice: this.currentVoice,
      volume: this.volume,
      activeContexts: this.audioContexts.length
    };
  }
}

// 创建全局简化音频管理器实例
const simpleAudioManager = new SimpleAudioManager();

module.exports = {
  SimpleAudioManager,
  audioManager: simpleAudioManager
};