// utils/metronome.js - 节拍器管理工具
const { audioManager } = require('./simple-audio.js');

class Metronome {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
    this.bpm = 120;
    this.beatType = '4/4拍';
    this.currentBeat = 1;
    this.totalBeats = 0;
    this.measureCount = 0;
    
    // 节拍模式配置
    this.beatPatterns = {
      '2/4拍': { beatsPerMeasure: 2, accents: [1] },
      '3/4拍': { beatsPerMeasure: 3, accents: [1] },
      '4/4拍': { beatsPerMeasure: 4, accents: [1] },
      '6/8拍': { beatsPerMeasure: 6, accents: [1, 4] }
    };
    
    // 回调函数
    this.onBeat = null;
    this.onMeasure = null;
    this.onAccent = null;
    
    // 性能监控
    this.lastBeatTime = 0;
    this.beatAccuracy = [];
  }

  // 设置BPM
  setBPM(bpm) {
    if (bpm < 60 || bpm > 200) {
      throw new Error('BPM必须在60-200之间');
    }
    
    this.bpm = bpm;
    
    // 如果正在运行，重新启动以应用新的BPM
    if (this.isRunning) {
      this.stop();
      this.start();
    }
  }

  // 设置节拍类型
  setBeatType(beatType) {
    if (!this.beatPatterns[beatType]) {
      throw new Error(`不支持的节拍类型: ${beatType}`);
    }
    
    this.beatType = beatType;
    this.currentBeat = 1; // 重置当前拍子
  }

  // 设置音色
  setVoice(voice) {
    audioManager.setVoice(voice);
  }

  // 启动节拍器
  start() {
    if (this.isRunning) {
      console.warn('节拍器已在运行');
      return;
    }

    this.isRunning = true;
    this.currentBeat = 1;
    this.lastBeatTime = Date.now();
    
    // 计算节拍间隔（毫秒）
    const interval = 60000 / this.bpm;
    
    // 播放开始提示音
    audioManager.playNotification('start');
    
    // 设置定时器
    this.intervalId = setInterval(() => {
      this.playBeat();
    }, interval);
    
    console.log(`节拍器启动: ${this.bpm} BPM, ${this.beatType}`);
  }

  // 停止节拍器
  stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    // 播放停止提示音
    audioManager.playNotification('pause');
    
    console.log('节拍器已停止');
  }

  // 播放节拍
  playBeat() {
    const now = Date.now();
    const pattern = this.beatPatterns[this.beatType];
    const isAccent = pattern.accents.includes(this.currentBeat);
    
    // 播放节拍音
    audioManager.playBeat(isAccent, this.beatType);
    
    // 记录节拍精度
    if (this.lastBeatTime > 0) {
      const expectedInterval = 60000 / this.bpm;
      const actualInterval = now - this.lastBeatTime;
      const accuracy = Math.abs(actualInterval - expectedInterval);
      this.beatAccuracy.push(accuracy);
      
      // 只保留最近100次的精度记录
      if (this.beatAccuracy.length > 100) {
        this.beatAccuracy.shift();
      }
    }
    
    this.lastBeatTime = now;
    this.totalBeats++;
    
    // 触发回调
    if (this.onBeat) {
      this.onBeat(this.currentBeat, isAccent, this.totalBeats);
    }
    
    if (isAccent && this.onAccent) {
      this.onAccent(this.currentBeat, this.measureCount);
    }
    
    // 更新当前拍子
    this.currentBeat++;
    if (this.currentBeat > pattern.beatsPerMeasure) {
      this.currentBeat = 1;
      this.measureCount++;
      
      if (this.onMeasure) {
        this.onMeasure(this.measureCount);
      }
    }
  }

  // 获取当前状态
  getStatus() {
    return {
      isRunning: this.isRunning,
      bpm: this.bpm,
      beatType: this.beatType,
      currentBeat: this.currentBeat,
      totalBeats: this.totalBeats,
      measureCount: this.measureCount,
      beatsPerMeasure: this.beatPatterns[this.beatType].beatsPerMeasure
    };
  }

  // 获取节拍精度统计
  getAccuracyStats() {
    if (this.beatAccuracy.length === 0) {
      return null;
    }
    
    const sum = this.beatAccuracy.reduce((a, b) => a + b, 0);
    const avg = sum / this.beatAccuracy.length;
    const max = Math.max(...this.beatAccuracy);
    const min = Math.min(...this.beatAccuracy);
    
    return {
      average: Math.round(avg),
      maximum: Math.round(max),
      minimum: Math.round(min),
      samples: this.beatAccuracy.length
    };
  }

  // 重置统计
  reset() {
    this.currentBeat = 1;
    this.totalBeats = 0;
    this.measureCount = 0;
    this.beatAccuracy = [];
    this.lastBeatTime = 0;
  }

  // 设置初始节拍计数（用于暂停后继续）
  setTotalBeats(count) {
    this.totalBeats = count || 0;
    console.log('设置节拍器初始计数:', this.totalBeats);
  }

  // 设置回调函数
  setCallbacks(callbacks) {
    this.onBeat = callbacks.onBeat || null;
    this.onMeasure = callbacks.onMeasure || null;
    this.onAccent = callbacks.onAccent || null;
  }

  // 临时改变速度（渐变）
  changeTempoGradually(targetBPM, durationMs = 5000) {
    if (!this.isRunning) {
      this.setBPM(targetBPM);
      return;
    }

    const startBPM = this.bpm;
    const bpmDiff = targetBPM - startBPM;
    const steps = 50; // 50步渐变
    const stepDuration = durationMs / steps;
    const bpmStep = bpmDiff / steps;
    
    let currentStep = 0;
    
    const gradualChange = setInterval(() => {
      currentStep++;
      const newBPM = Math.round(startBPM + (bpmStep * currentStep));
      
      if (currentStep >= steps) {
        clearInterval(gradualChange);
        this.setBPM(targetBPM);
      } else {
        this.setBPM(newBPM);
      }
    }, stepDuration);
  }

  // 节拍器校准
  calibrate() {
    console.log('开始节拍器校准...');
    
    // 播放校准序列
    const calibrationBeats = 8;
    let beatCount = 0;
    
    const calibrationInterval = setInterval(() => {
      audioManager.playBeat(beatCount % 4 === 0, '4/4拍');
      beatCount++;
      
      if (beatCount >= calibrationBeats) {
        clearInterval(calibrationInterval);
        console.log('节拍器校准完成');
      }
    }, 500); // 120 BPM
  }

  // 获取建议的BPM范围
  static getRecommendedBPM(activityType) {
    const recommendations = {
      '超慢跑': { min: 80, max: 120, optimal: 100 },
      '慢跑': { min: 120, max: 140, optimal: 130 },
      '中速跑': { min: 140, max: 160, optimal: 150 },
      '快跑': { min: 160, max: 180, optimal: 170 },
      '冲刺': { min: 180, max: 200, optimal: 190 }
    };
    
    return recommendations[activityType] || recommendations['慢跑'];
  }

  // 根据步频计算建议BPM
  static calculateBPMFromCadence(cadence) {
    // 步频通常是每分钟步数，节拍可以是步频的一半（每只脚一拍）
    return Math.round(cadence / 2);
  }
}

// 创建全局节拍器实例
const metronome = new Metronome();

module.exports = {
  Metronome,
  metronome
};